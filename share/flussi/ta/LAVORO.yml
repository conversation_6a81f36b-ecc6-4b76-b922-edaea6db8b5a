---
tipo_attivita: LAVORO
tipi_sistema:
  - CAVO
  - NODO
  - CANTIERE
  - LAVORO
transizioni:
  - stato_iniziale: START
    action: APERTURA
    stato_finale: APERTA
    gruppi:
      - AT
      - AT03
      - ROOT
    tdta:
      - posizione: 0
        descrizione: customerId
        etichetta: ''
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: contractId
        etichetta: ''
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 2
        descrizione: projectId
        etichetta: ''
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 3
        descrizione: estimatedDuration
        etichetta: ''
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 4
        descrizione: cadastralCode
        etichetta: ''
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 5
        descrizione: pop
        etichetta: ''
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 6
        descrizione: popId
        etichetta: ''
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 7
        descrizione: ring
        etichetta: ''
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 8
        descrizione: ringId
        etichetta: ''
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 9
        descrizione: workingGroupCode
        etichetta: ''
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 10
        descrizione: address
        etichetta: ''
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 11
        descrizione: maker
        etichetta: ''
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 12
        descrizione: slaStart
        etichetta: ''
        tipo_ui: ISODAT
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 13
        descrizione: slaEnd
        etichetta: ''
        tipo_ui: ISODAT
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 14
        descrizione: startPlannedDate
        etichetta: ''
        tipo_ui: ISODAT
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 15
        descrizione: endPlannedDate
        etichetta: ''
        tipo_ui: ISODAT
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 16
        descrizione: subContractCode
        etichetta: ''
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 17
        descrizione: subContractName
        etichetta: ''
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 18
        descrizione: onFieldIntegrationDisabled
        etichetta: ''
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 19
        descrizione: teamId
        etichetta: ''
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 20
        descrizione: teamName
        etichetta: ''
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 21
        descrizione: invalidatePreviousReferences
        etichetta: Invalida riferimenti pregresso
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 22
        descrizione: test
        etichetta: Collaudo
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 23
        descrizione: updateDatabase
        etichetta: Agg. Banca Dati
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 24
        descrizione: civil
        etichetta: Lavori Civili
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 25
        descrizione: junction
        etichetta: Giunzione
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 26
        descrizione: cableLaying
        etichetta: Posa Cavo
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 27
        descrizione: survey
        etichetta: Sopralluogo
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 28
        descrizione: design
        etichetta: Progettazione
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 29
        descrizione: flagFIR
        etichetta: 'Previste attività di scavo, ripristino, smantellamento'
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 30
        descrizione: updateDatabaseF2
        etichetta: updateDatabaseF2
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 31
        descrizione: opticalConnectionOLT
        etichetta: opticalConnectionOLT
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 32
        descrizione: opticalConnectionOSU
        etichetta: opticalConnectionOSU
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 33
        descrizione: updateDatabaseF1
        etichetta: updateDatabaseF1
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 34
        descrizione: restoration
        etichetta: restoration
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 35
        descrizione: patchCord
        etichetta: Bretellaggio
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 36
        descrizione: pathSurvey
        etichetta: Visita Tracciato
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 37
        descrizione: installationPlaceSurvey
        etichetta: Ispezione Sede Posa
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 38
        descrizione: installationReview
        etichetta: Revisione Impianti
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 39
        descrizione: worksManning
        etichetta: Sorveglianza Lavori
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 40
        descrizione: measurements
        etichetta: Misure
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 41
        descrizione: quarterSummary
        etichetta: Riepilogo Trimestre
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 42
        descrizione: defectWithDisservice
        etichetta: Guasto Disservito
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 43
        descrizione: defectWithoutDisservice
        etichetta: Guasto Non Disservito
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 44
        descrizione: infrastructure
        etichetta: Infrastruttura
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 45
        descrizione: laying
        etichetta: Posa
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 46
        descrizione: testApp
        etichetta: Collaudo App
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 47
        descrizione: testOTDR
        etichetta: Collaudo OTDR
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 48
        descrizione: generic
        etichetta: Generico
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 49
        descrizione: planning
        etichetta: Impresa Progettazione
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 50
        descrizione: __DTC_CMI
        etichetta: DOC. MISURE
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 51
        descrizione: __DTC_DCO
        etichetta: DOC. DOCUMENTAZIONE COLLAUDO
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 52
        descrizione: __DTC_DOF
        etichetta: DOC. DOCUMENTAZIONE FOTOGRAFICA
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 53
        descrizione: __DTC_VAR
        etichetta: DOC. VARIE
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 54
        descrizione: __DTC_WKI
        etichetta: DOC. WALK IN
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 55
        descrizione: __DTC_FIR
        etichetta: DOC. FIR
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 56
        descrizione: __DTC_SIN
        etichetta: DOC. SINOTTICO
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 57
        descrizione: __DTC_PLAN
        etichetta: DOC. PLANIMETRIA
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 58
        descrizione: ref00
        etichetta: Riferimento 00
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 59
        descrizione: ref01
        etichetta: Riferimento 01
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 60
        descrizione: fieldService
        etichetta: Gestione field
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 61
        descrizione: accountingOperation
        etichetta: Operazione contabile
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 62
        descrizione: cannotCloseWork
        etichetta: Chiusura disabilitata
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 63
        descrizione: takeInCharge
        etichetta: Presa in carico
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
  - stato_iniziale: ___ANY_STATUS___
    action: AGGIORNAMENTO_DATI
    stato_finale: ___ANY_STATUS___
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: cannotCloseWork
        etichetta: Chiusura disabilitata
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: Y
  - stato_iniziale: ___ANY_STATUS___
    action: INVALIDAZIONE
    stato_finale: INVALIDATO
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: invalidateActivityId
        etichetta: Id attività invalidazione
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: ___ANY_STATUS___
    action: SOCIETARIZZAZIONE_CLIENTE
    stato_finale: ___ANY_STATUS___
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: projectId
        etichetta: ''
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: projectIdOld
        etichetta: ''
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: APERTA
    action: LAVORABILE_DA_SIRTI
    stato_finale: IN_LAVORAZIONE_SIRTI
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: slaStart
        etichetta: ''
        tipo_ui: ISODAT
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: slaEnd
        etichetta: ''
        tipo_ui: ISODAT
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: APERTA
    action: LAVORABILE_DA_SUBAPPALTO
    stato_finale: IN_LAVORAZIONE_SUBAPPALTO
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: startPlannedDate
        etichetta: ''
        tipo_ui: ISODAT
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: endPlannedDate
        etichetta: ''
        tipo_ui: ISODAT
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 2
        descrizione: subContractCode
        etichetta: ''
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 3
        descrizione: subContractName
        etichetta: ''
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: IN_LAVORAZIONE_SIRTI
    action: ANNULLAMENTO_SIRTI_OFFLINE
    stato_finale: ANNULLATO
    gruppi:
      - ADMIN
      - AT
      - AT03
      - ROOT
    tdta:
      - posizione: 0
        descrizione: cancelReason
        etichetta: Motivazione annullamento
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: 'LAVORI KO,MANCANZA PERMESSO PRIVATO,MANCANZA PERMESSO ENTE,TUBAZIONE INTERROTTA,STABILE INACCESSIBILE,ALTRO (CON NOTE),KO - Eccessiva Onerosità,KO - Utente non FTTH (Banca / Caserma / Scuola etc...),KO - Distributore Rame Non Presente,KO - Stabile disabitato/in demolizione,KO - Permesso Privato,KO - Permesso Ente,KO - In attesa Variante progetto,KO - In attesa autorizzazione utilizzo infrastruttura altro ente (IRU / illuminazione pubblica)'
      - posizione: 1
        descrizione: cancelNote
        etichetta: Note annullamento
        tipo_ui: 'MEMO  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 2
        descrizione: cannotCloseWork
        etichetta: Chiusura disabilitata
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: Y
  - stato_iniziale: IN_LAVORAZIONE_SIRTI
    action: CHIUSURA_PARZIALE
    stato_finale: IN_LAVORAZIONE_SIRTI
    gruppi:
      - ADMIN
      - REMOTE_AUTOMATION
      - ROOT
    tdta:
      - posizione: 0
        descrizione: teamId
        etichetta: ''
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: Y
      - posizione: 1
        descrizione: teamName
        etichetta: ''
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: Y
      - posizione: 2
        descrizione: fieldServiceId
        etichetta: Id servizio da campo
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 3
        descrizione: fieldServiceUsername
        etichetta: Utenza servizio su campo
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 4
        descrizione: fieldServiceUserid
        etichetta: Id utenza servizio su campo
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 5
        descrizione: fieldServiceTimestamp
        etichetta: Timestamp servizio su campo
        tipo_ui: ISODAT
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 6
        descrizione: fieldGeoLocation
        etichetta: Georeferenziazione su campo
        tipo_ui: LATLNG
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "0.00000,0.000000"
          }
  - stato_iniziale: IN_LAVORAZIONE_SIRTI
    action: EVENTO_ASSEGNAZIONE
    stato_finale: IN_LAVORAZIONE_SIRTI
    gruppi:
      - ADMIN
      - REMOTE_EVENT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: teamId
        etichetta: ''
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 1
        descrizione: teamName
        etichetta: ''
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 2
        descrizione: fieldServiceId
        etichetta: Id servizio da campo
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 3
        descrizione: fieldServiceUsername
        etichetta: Utenza servizio su campo
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 4
        descrizione: fieldServiceUserid
        etichetta: Id utenza servizio su campo
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 5
        descrizione: fieldServiceTimestamp
        etichetta: Timestamp servizio su campo
        tipo_ui: ISODAT
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 6
        descrizione: fieldGeoLocation
        etichetta: Georeferenziazione su campo
        tipo_ui: LATLNG
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "0.00000,0.000000"
          }
  - stato_iniziale: IN_LAVORAZIONE_SIRTI
    action: EVENTO_AVVIO_LAVORI
    stato_finale: IN_LAVORAZIONE_SIRTI
    gruppi:
      - ADMIN
      - REMOTE_EVENT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: teamId
        etichetta: ''
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 1
        descrizione: teamName
        etichetta: ''
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 2
        descrizione: fieldServiceId
        etichetta: Id servizio da campo
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 3
        descrizione: fieldServiceUsername
        etichetta: Utenza servizio su campo
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 4
        descrizione: fieldServiceUserid
        etichetta: Id utenza servizio su campo
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 5
        descrizione: fieldServiceTimestamp
        etichetta: Timestamp servizio su campo
        tipo_ui: ISODAT
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 6
        descrizione: fieldGeoLocation
        etichetta: Georeferenziazione su campo
        tipo_ui: LATLNG
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "0.00000,0.000000"
          }
  - stato_iniziale: IN_LAVORAZIONE_SIRTI
    action: FINE_LAVORI
    stato_finale: ESPLETATO
    gruppi:
      - ADMIN
      - AT
      - AT03
      - ROOT
    tdta:
      - posizione: 0
        descrizione: startWorkDate
        etichetta: ''
        tipo_ui: ISODAT
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: endWorkDate
        etichetta: ''
        tipo_ui: ISODAT
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 2
        descrizione: flagFIR
        etichetta: 'Previste attività di scavo, ripristino, smantellamento'
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: Y
        nascosto: N
        prepopola: Y
      - posizione: 3
        descrizione: anomaliesDangers
        etichetta: Anomalie / Pericoli rilevati
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
            "type": "const",
            "value": "NESSUNA"
          }
      - posizione: 4
        descrizione: thirdPartiesSystemSignalling
        etichetta: Segnalazione impianto a terzi
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
            "type": "const",
            "value": "NESSUNA"
          }
      - posizione: 5
        descrizione: notes
        etichetta: Osservazioni
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
            "type": "const",
            "value": "NESSUNA"
          }
  - stato_iniziale: IN_LAVORAZIONE_SIRTI
    action: FINE_LAVORI_KO
    stato_finale: ANNULLATO
    gruppi:
      - ADMIN
      - REMOTE_AUTOMATION
      - ROOT
    tdta:
      - posizione: 0
        descrizione: cancelReason
        etichetta: Motivazione annullamento
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: 'LAVORI KO,MANCANZA PERMESSO PRIVATO,MANCANZA PERMESSO ENTE,TUBAZIONE INTERROTTA,STABILE INACCESSIBILE,ALTRO (CON NOTE),KO - Eccessiva Onerosità,KO - Utente non FTTH (Banca / Caserma / Scuola etc...),KO - Distributore Rame Non Presente,KO - Stabile disabitato/in demolizione,KO - Permesso Privato,KO - Permesso Ente,KO - In attesa Variante progetto,KO - In attesa autorizzazione utilizzo infrastruttura altro ente (IRU / illuminazione pubblica)'
      - posizione: 1
        descrizione: cancelNote
        etichetta: Note annullamento
        tipo_ui: 'MEMO  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 2
        descrizione: teamId
        etichetta: ''
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: Y
      - posizione: 3
        descrizione: teamName
        etichetta: ''
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: Y
      - posizione: 4
        descrizione: fieldServiceId
        etichetta: Id servizio da campo
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 5
        descrizione: fieldServiceUsername
        etichetta: Utenza servizio su campo
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 6
        descrizione: fieldServiceUserid
        etichetta: Id utenza servizio su campo
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 7
        descrizione: fieldServiceTimestamp
        etichetta: Timestamp servizio su campo
        tipo_ui: ISODAT
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 8
        descrizione: fieldGeoLocation
        etichetta: Georeferenziazione su campo
        tipo_ui: LATLNG
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "0.00000,0.000000"
          }
  - stato_iniziale: IN_LAVORAZIONE_SIRTI
    action: FINE_LAVORI_OK
    stato_finale: ESPLETATO
    gruppi:
      - ADMIN
      - REMOTE_AUTOMATION
      - ROOT
    tdta:
      - posizione: 0
        descrizione: teamId
        etichetta: ''
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: Y
      - posizione: 1
        descrizione: teamName
        etichetta: ''
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: Y
      - posizione: 2
        descrizione: fieldServiceId
        etichetta: Id servizio da campo
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 3
        descrizione: fieldServiceUsername
        etichetta: Utenza servizio su campo
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 4
        descrizione: fieldServiceUserid
        etichetta: Id utenza servizio su campo
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 5
        descrizione: fieldServiceTimestamp
        etichetta: Timestamp servizio su campo
        tipo_ui: ISODAT
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 6
        descrizione: fieldGeoLocation
        etichetta: Georeferenziazione su campo
        tipo_ui: LATLNG
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "0.00000,0.000000"
          }
  - stato_iniziale: IN_LAVORAZIONE_SIRTI
    action: NON_NECESSARIO
    stato_finale: NON_NECESSARIO
    gruppi:
      - ADMIN
      - AT
      - AT03
      - ROOT
      - SERVICE
    tdta:
      - posizione: 0
        descrizione: cannotCloseWork
        etichetta: Chiusura disabilitata
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: Y
  - stato_iniziale: IN_LAVORAZIONE_SIRTI
    action: SOSPENSIONE_LAVORI
    stato_finale: SOSPESO_SIRTI
    gruppi:
      - ADMIN
      - AT
      - AT03
      - REMOTE_AUTOMATION
      - ROOT
    tdta:
      - posizione: 0
        descrizione: suspensionReason
        etichetta: Motivo sospensione
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: fieldServiceId
        etichetta: Id servizio da campo
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 2
        descrizione: fieldServiceUsername
        etichetta: Utenza servizio su campo
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 3
        descrizione: fieldServiceUserid
        etichetta: Id utenza servizio su campo
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 4
        descrizione: fieldServiceTimestamp
        etichetta: Timestamp servizio su campo
        tipo_ui: ISODAT
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 5
        descrizione: fieldGeoLocation
        etichetta: Georeferenziazione su campo
        tipo_ui: LATLNG
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "0.00000,0.000000"
          }
      - posizione: 6
        descrizione: teamId
        etichetta: ''
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: Y
      - posizione: 7
        descrizione: teamName
        etichetta: ''
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: Y
      - posizione: 8
        descrizione: cannotCloseWork
        etichetta: Chiusura disabilitata
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: Y
  - stato_iniziale: IN_LAVORAZIONE_SUBAPPALTO
    action: ANNULLAMENTO
    stato_finale: ANNULLATO
    gruppi:
      - ADMIN
      - AT
      - AT03
      - ROOT
    tdta:
      - posizione: 0
        descrizione: cancelReason
        etichetta: Motivazione annullamento
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: 'LAVORI KO,MANCANZA PERMESSO PRIVATO,MANCANZA PERMESSO ENTE,TUBAZIONE INTERROTTA,STABILE INACCESSIBILE,ALTRO (CON NOTE),KO - Eccessiva Onerosità,KO - Utente non FTTH (Banca / Caserma / Scuola etc...),KO - Distributore Rame Non Presente,KO - Stabile disabitato/in demolizione,KO - Permesso Privato,KO - Permesso Ente,KO - In attesa Variante progetto,KO - In attesa autorizzazione utilizzo infrastruttura altro ente (IRU / illuminazione pubblica)'
      - posizione: 1
        descrizione: cancelNote
        etichetta: Note annullamento
        tipo_ui: 'MEMO  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 2
        descrizione: cannotCloseWork
        etichetta: Chiusura disabilitata
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: Y
  - stato_iniziale: IN_LAVORAZIONE_SUBAPPALTO
    action: FINE_LAVORI
    stato_finale: ESPLETATO
    gruppi:
      - ADMIN
      - AT
      - AT03
      - ROOT
      - SERVICE
    tdta:
      - posizione: 0
        descrizione: startWorkDate
        etichetta: ''
        tipo_ui: ISODAT
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: endWorkDate
        etichetta: ''
        tipo_ui: ISODAT
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 2
        descrizione: flagFIR
        etichetta: 'Previste attività di scavo, ripristino, smantellamento'
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: Y
        nascosto: N
        prepopola: Y
      - posizione: 3
        descrizione: anomaliesDangers
        etichetta: Anomalie / Pericoli rilevati
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
            "type": "const",
            "value": "NESSUNA"
          }
      - posizione: 4
        descrizione: thirdPartiesSystemSignalling
        etichetta: Segnalazione impianto a terzi
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
            "type": "const",
            "value": "NESSUNA"
          }
      - posizione: 5
        descrizione: notes
        etichetta: Osservazioni
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
            "type": "const",
            "value": "NESSUNA"
          }
  - stato_iniziale: IN_LAVORAZIONE_SUBAPPALTO
    action: NON_NECESSARIO
    stato_finale: NON_NECESSARIO
    gruppi:
      - ADMIN
      - AT
      - AT03
      - ROOT
      - SERVICE
    tdta:
      - posizione: 0
        descrizione: cannotCloseWork
        etichetta: Chiusura disabilitata
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: Y
  - stato_iniziale: IN_LAVORAZIONE_SUBAPPALTO
    action: PRESA_IN_CARICO
    stato_finale: IN_LAVORAZIONE_SUBAPPALTO
    gruppi:
      - ADMIN
      - ROOT
      - SERVICE
    tdta:
      - posizione: 0
        descrizione: takeInCharge
        etichetta: Presa in carico
        tipo_ui: 'BOOL  '
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 1
        descrizione: cannotCloseWork
        etichetta: Chiusura disabilitata
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
  - stato_iniziale: IN_LAVORAZIONE_SUBAPPALTO
    action: SOSPENSIONE_LAVORI
    stato_finale: SOSPESO_SUBAPPALTO
    gruppi:
      - ADMIN
      - AT
      - AT03
      - ROOT
      - SERVICE
    tdta:
      - posizione: 0
        descrizione: suspensionReason
        etichetta: Motivo sospensione
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: fieldServiceId
        etichetta: Id servizio da campo
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 2
        descrizione: fieldServiceUsername
        etichetta: Utenza servizio su campo
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 3
        descrizione: fieldServiceUserid
        etichetta: Id utenza servizio su campo
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 4
        descrizione: fieldServiceTimestamp
        etichetta: Timestamp servizio su campo
        tipo_ui: ISODAT
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 5
        descrizione: fieldGeoLocation
        etichetta: Georeferenziazione su campo
        tipo_ui: LATLNG
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "0.00000,0.000000"
          }
      - posizione: 6
        descrizione: teamId
        etichetta: ''
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: Y
      - posizione: 7
        descrizione: teamName
        etichetta: ''
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: Y
      - posizione: 8
        descrizione: cannotCloseWork
        etichetta: Chiusura disabilitata
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: Y
  - stato_iniziale: SOSPESO_SIRTI
    action: ANNULLAMENTO_SIRTI_OFFLINE
    stato_finale: ANNULLATO
    gruppi:
      - ADMIN
      - AT
      - AT03
      - ROOT
    tdta:
      - posizione: 0
        descrizione: cancelReason
        etichetta: Motivazione annullamento
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: 'LAVORI KO,MANCANZA PERMESSO PRIVATO,MANCANZA PERMESSO ENTE,TUBAZIONE INTERROTTA,STABILE INACCESSIBILE,ALTRO (CON NOTE),KO - Eccessiva Onerosità,KO - Utente non FTTH (Banca / Caserma / Scuola etc...),KO - Distributore Rame Non Presente,KO - Stabile disabitato/in demolizione,KO - Permesso Privato,KO - Permesso Ente,KO - In attesa Variante progetto,KO - In attesa autorizzazione utilizzo infrastruttura altro ente (IRU / illuminazione pubblica)'
      - posizione: 1
        descrizione: cancelNote
        etichetta: Note annullamento
        tipo_ui: 'MEMO  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 2
        descrizione: cannotCloseWork
        etichetta: Chiusura disabilitata
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: Y
  - stato_iniziale: IN_LAVORAZIONE_SUBAPPALTO
    action: SOSPENSIONE_BD
    stato_finale: SOSPESO_SUBAPPALTO
    gruppi:
      - ADMIN
      - AT
      - ROOT
      - SERVICE
    tdta:
      - posizione: 0
        descrizione: suspensionReason
        etichetta: Causale sospensione
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: 'Blocco sistema,Attesa riscontro gruppo operativo (Territorio),Lock,Path su PTE'
      - posizione: 1
        descrizione: notes
        etichetta: Osservazioni
        tipo_ui: 'MEMO  '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: SOSPESO_SIRTI
    action: EVENTO_ASSEGNAZIONE
    stato_finale: SOSPESO_SIRTI
    gruppi:
      - ADMIN
      - REMOTE_EVENT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: teamId
        etichetta: ''
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 1
        descrizione: teamName
        etichetta: ''
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 2
        descrizione: fieldServiceId
        etichetta: Id servizio da campo
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 3
        descrizione: fieldServiceUsername
        etichetta: Utenza servizio su campo
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 4
        descrizione: fieldServiceUserid
        etichetta: Id utenza servizio su campo
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 5
        descrizione: fieldServiceTimestamp
        etichetta: Timestamp servizio su campo
        tipo_ui: ISODAT
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 6
        descrizione: fieldGeoLocation
        etichetta: Georeferenziazione su campo
        tipo_ui: LATLNG
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "0.00000,0.000000"
          }
  - stato_iniziale: SOSPESO_SIRTI
    action: EVENTO_AVVIO_LAVORI
    stato_finale: IN_LAVORAZIONE_SIRTI
    gruppi:
      - ADMIN
      - REMOTE_EVENT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: teamId
        etichetta: ''
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 1
        descrizione: teamName
        etichetta: ''
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 2
        descrizione: fieldServiceId
        etichetta: Id servizio da campo
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 3
        descrizione: fieldServiceUsername
        etichetta: Utenza servizio su campo
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 4
        descrizione: fieldServiceUserid
        etichetta: Id utenza servizio su campo
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 5
        descrizione: fieldServiceTimestamp
        etichetta: Timestamp servizio su campo
        tipo_ui: ISODAT
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 6
        descrizione: fieldGeoLocation
        etichetta: Georeferenziazione su campo
        tipo_ui: LATLNG
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "0.00000,0.000000"
          }
  - stato_iniziale: SOSPESO_SIRTI
    action: FINE_LAVORI
    stato_finale: ESPLETATO
    gruppi:
      - ADMIN
      - AT
      - AT03
      - ROOT
    tdta:
      - posizione: 0
        descrizione: startWorkDate
        etichetta: ''
        tipo_ui: ISODAT
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: endWorkDate
        etichetta: ''
        tipo_ui: ISODAT
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 2
        descrizione: flagFIR
        etichetta: 'Previste attività di scavo, ripristino, smantellamento'
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: Y
        nascosto: N
        prepopola: Y
      - posizione: 3
        descrizione: anomaliesDangers
        etichetta: Anomalie / Pericoli rilevati
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
            "type": "const",
            "value": "NESSUNA"
          }
      - posizione: 4
        descrizione: thirdPartiesSystemSignalling
        etichetta: Segnalazione impianto a terzi
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
            "type": "const",
            "value": "NESSUNA"
          }
      - posizione: 5
        descrizione: notes
        etichetta: Osservazioni
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
            "type": "const",
            "value": "NESSUNA"
          }
  - stato_iniziale: SOSPESO_SIRTI
    action: NON_NECESSARIO
    stato_finale: NON_NECESSARIO
    gruppi:
      - ADMIN
      - AT
      - AT03
      - ROOT
      - SERVICE
    tdta:
      - posizione: 0
        descrizione: cannotCloseWork
        etichetta: Chiusura disabilitata
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: Y
  - stato_iniziale: SOSPESO_SIRTI
    action: RIPRESA_LAVORI
    stato_finale: IN_LAVORAZIONE_SIRTI
    gruppi:
      - ADMIN
      - AT
      - AT03
      - ROOT
    tdta:
      - posizione: 0
        descrizione: fieldServiceId
        etichetta: Id servizio da campo
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 1
        descrizione: fieldServiceUsername
        etichetta: Utenza servizio su campo
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 2
        descrizione: fieldServiceUserid
        etichetta: Id utenza servizio su campo
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 3
        descrizione: fieldServiceTimestamp
        etichetta: Timestamp servizio su campo
        tipo_ui: ISODAT
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 4
        descrizione: fieldGeoLocation
        etichetta: Georeferenziazione su campo
        tipo_ui: LATLNG
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "0.00000,0.000000"
          }
      - posizione: 5
        descrizione: cannotCloseWork
        etichetta: Chiusura disabilitata
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: Y
  - stato_iniziale: SOSPESO_SUBAPPALTO
    action: ANNULLAMENTO
    stato_finale: ANNULLATO
    gruppi:
      - ADMIN
      - AT
      - AT03
      - ROOT
    tdta:
      - posizione: 0
        descrizione: cancelReason
        etichetta: Motivazione annullamento
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: 'LAVORI KO,MANCANZA PERMESSO PRIVATO,MANCANZA PERMESSO ENTE,TUBAZIONE INTERROTTA,STABILE INACCESSIBILE,ALTRO (CON NOTE),KO - Eccessiva Onerosità,KO - Utente non FTTH (Banca / Caserma / Scuola etc...),KO - Distributore Rame Non Presente,KO - Stabile disabitato/in demolizione,KO - Permesso Privato,KO - Permesso Ente,KO - In attesa Variante progetto,KO - In attesa autorizzazione utilizzo infrastruttura altro ente (IRU / illuminazione pubblica)'
      - posizione: 1
        descrizione: cancelNote
        etichetta: Note annullamento
        tipo_ui: 'MEMO  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 2
        descrizione: cannotCloseWork
        etichetta: Chiusura disabilitata
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: Y
  - stato_iniziale: SOSPESO_SUBAPPALTO
    action: FINE_LAVORI
    stato_finale: ESPLETATO
    gruppi:
      - ADMIN
      - AT
      - AT03
      - ROOT
      - SERVICE
    tdta:
      - posizione: 0
        descrizione: startWorkDate
        etichetta: ''
        tipo_ui: ISODAT
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: endWorkDate
        etichetta: ''
        tipo_ui: ISODAT
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 2
        descrizione: flagFIR
        etichetta: 'Previste attività di scavo, ripristino, smantellamento'
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: Y
        nascosto: N
        prepopola: Y
      - posizione: 3
        descrizione: anomaliesDangers
        etichetta: Anomalie / Pericoli rilevati
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
            "type": "const",
            "value": "NESSUNA"
          }
      - posizione: 4
        descrizione: thirdPartiesSystemSignalling
        etichetta: Segnalazione impianto a terzi
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
            "type": "const",
            "value": "NESSUNA"
          }
      - posizione: 5
        descrizione: notes
        etichetta: Osservazioni
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
            "type": "const",
            "value": "NESSUNA"
          }
  - stato_iniziale: SOSPESO_SUBAPPALTO
    action: NON_NECESSARIO
    stato_finale: NON_NECESSARIO
    gruppi:
      - ADMIN
      - AT
      - AT03
      - ROOT
      - SERVICE
    tdta:
      - posizione: 0
        descrizione: cannotCloseWork
        etichetta: Chiusura disabilitata
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: Y
  - stato_iniziale: SOSPESO_SUBAPPALTO
    action: RIPRESA_LAVORI
    stato_finale: IN_LAVORAZIONE_SUBAPPALTO
    gruppi:
      - ADMIN
      - AT
      - AT03
      - ROOT
      - SERVICE
    tdta:
      - posizione: 0
        descrizione: fieldServiceId
        etichetta: Id servizio da campo
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 1
        descrizione: fieldServiceUsername
        etichetta: Utenza servizio su campo
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 2
        descrizione: fieldServiceUserid
        etichetta: Id utenza servizio su campo
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 3
        descrizione: fieldServiceTimestamp
        etichetta: Timestamp servizio su campo
        tipo_ui: ISODAT
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 4
        descrizione: fieldGeoLocation
        etichetta: Georeferenziazione su campo
        tipo_ui: LATLNG
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "0.00000,0.000000"
          }
      - posizione: 5
        descrizione: cannotCloseWork
        etichetta: Chiusura disabilitata
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: Y
