package API::ART::APP::Activity::LAVORO::Binding::Action::SOSPENSIONE_BD;

use strict;
use warnings;
use Carp qw(verbose croak);
use JSON;

use base qw(API::ART::Activity::Binding::Action::Base);
sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

sub can_do {
	my ( $self, $activity) = @_;

	my $art = $self->{ART};

	if (
		$activity->activity_property('maker') eq 'Subcontract' &&
		$activity->activity_property('workType') eq 'PTE' &&
	){
	$art->last_error(__x("Unable to suspend activity when field service enabled"));
	return undef;
	}

	return 1;
}

1;
