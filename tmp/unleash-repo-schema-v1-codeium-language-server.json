{"ACTIVITY_CONTEXT_WEIGHT": {"name": "ACTIVITY_CONTEXT_WEIGHT", "type": "experiment", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "ACTIVITY_CONTEXT_WEIGHT", "rollout": "100", "stickiness": "random"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "API_SERVER_CLIENT_USE_HTTP_2": {"name": "API_SERVER_CLIENT_USE_HTTP_2", "type": "kill-switch", "description": "Enables HTTP/2 within the language server (eg. for communication with the API server).", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "API_SERVER_CLIENT_USE_HTTP_2", "rollout": "100", "stickiness": "installationId"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "API_SERVER_CUTOFF": {"name": "API_SERVER_CUTOFF", "type": "experiment", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "API_SERVER_CUTOFF", "rollout": "100", "stickiness": "random"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "API_SERVER_ENABLE_MORE_LOGGING": {"name": "API_SERVER_ENABLE_MORE_LOGGING", "type": "release", "description": "", "enabled": false, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "API_SERVER_ENABLE_MORE_LOGGING", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "API_SERVER_LIVENESS_PROBE": {"name": "API_SERVER_LIVENESS_PROBE", "type": "release", "description": "", "enabled": false, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "API_SERVER_LIVENESS_PROBE", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "API_SERVER_LOG_CONNECT_CODES": {"name": "API_SERVER_LOG_CONNECT_CODES", "type": "operational", "description": "", "enabled": false, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "API_SERVER_LOG_CONNECT_CODES", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "codes", "payload": {"type": "string", "value": "invalid_argument"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "API_SERVER_PROMPT_CACHE_REPLICAS": {"name": "API_SERVER_PROMPT_CACHE_REPLICAS", "type": "experiment", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-insiders"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "API_SERVER_PROMPT_CACHE_REPLICAS", "rollout": "100", "stickiness": "sessionId"}, "segments": null, "variants": [{"name": "4_replicas", "payload": {"type": "string", "value": "4"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "sessionId", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-next"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "API_SERVER_PROMPT_CACHE_REPLICAS", "rollout": "100", "stickiness": "sessionId"}, "segments": null, "variants": [{"name": "4_replicas", "payload": {"type": "string", "value": "4"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "sessionId", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf"], "value": "", "caseInsensitive": false, "inverted": false}, {"contextName": "continent", "operator": "IN", "values": ["Europe", "North America"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "API_SERVER_PROMPT_CACHE_REPLICAS", "rollout": "100", "stickiness": "sessionId"}, "segments": null, "variants": [{"name": "4_replicas", "payload": {"type": "string", "value": "4"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "sessionId", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "API_SERVER_PROMPT_CACHE_REPLICAS", "rollout": "100", "stickiness": "sessionId"}, "segments": null, "variants": [{"name": "2_replicas", "payload": {"type": "string", "value": "2"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "sessionId", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": true}, "API_SERVER_VERBOSE_ERRORS": {"name": "API_SERVER_VERBOSE_ERRORS", "type": "operational", "description": "", "enabled": false, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "API_SERVER_VERBOSE_ERRORS", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "ATTRIBUTION_KILL_SWITCH": {"name": "ATTRIBUTION_KILL_SWITCH", "type": "kill-switch", "description": "Enable this kill switch to disable attribution for all users.", "enabled": false, "strategies": [{"id": 0, "name": "default", "constraints": [], "parameters": {}, "segments": null, "variants": []}, {"id": 0, "name": "userWithId", "constraints": [], "parameters": {"userIds": "b0ad0514-e7d5-4226-af68-21eaf43117a2"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "AUTOCOMPLETE_DEFAULT_DEBOUNCE_MS": {"name": "AUTOCOMPLETE_DEFAULT_DEBOUNCE_MS", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["vscode"], "value": "", "caseInsensitive": false, "inverted": false}, {"contextName": "teamsMode", "operator": "IN", "values": ["false"], "value": "", "caseInsensitive": false, "inverted": false}, {"contextName": "continent", "operator": "IN", "values": ["Asia"], "value": "", "caseInsensitive": false, "inverted": false}, {"contextName": "country", "operator": "IN", "values": ["singapore"], "value": "", "caseInsensitive": false, "inverted": true}], "parameters": {"groupId": "AUTOCOMPLETE_DEFAULT_DEBOUNCE_MS", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "payload": {"type": "number", "value": "600"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf"], "value": "", "caseInsensitive": false, "inverted": false}, {"contextName": "planName", "operator": "IN", "values": ["Free"], "value": "", "caseInsensitive": false, "inverted": true}], "parameters": {"groupId": "AUTOCOMPLETE_DEFAULT_DEBOUNCE_MS", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "fast", "payload": {"type": "string", "value": "20"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "AUTOCOMPLETE_DEFAULT_DEBOUNCE_MS", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "slow", "payload": {"type": "number", "value": "90"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "AUTOCOMPLETE_FAST_DEBOUNCE_MS": {"name": "AUTOCOMPLETE_FAST_DEBOUNCE_MS", "type": "release", "description": "", "enabled": false, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["vscode"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "AUTOCOMPLETE_FAST_DEBOUNCE_MS", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "semi-slow", "payload": {"type": "number", "value": "5000"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "AUTOCOMPLETE_FAST_DEBOUNCE_MS", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "5", "payload": {"type": "number", "value": "0"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "AUTOCOMPLETE_HIDDEN_ERROR_REGEX": {"name": "AUTOCOMPLETE_HIDDEN_ERROR_REGEX", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "AUTOCOMPLETE_HIDDEN_ERROR_REGEX", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "basic", "payload": {"type": "string", "value": "an internal error occurred"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "AUTO_BANNER_KILL_SWITCH": {"name": "AUTO_BANNER_KILL_SWITCH", "type": "kill-switch", "description": "Kill switch for content filter-based auto banning", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "AUTO_BANNER_KILL_SWITCH", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "BANNED_DOMAINS": {"name": "BANNED_DOMAINS", "type": "permission", "description": "Banned email domains for spam mitigation.", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "BANNED_DOMAINS", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "default", "payload": {"type": "string", "value": "jixiangry.com,kaixintt.com,yongyuanjk.com,lianzhanao.com,jonathana.com,joanjiaeuj.org,medicinexx.org,krystalyte.org,knockeduy.org,yvbtenp.top,senderosie.com,sttfarm.us,stpro89.us,hakatoy.us,pdpc.us,vvccu89.us,kataui.us,starbip.us,hotoph89.us,bpkt.us,nataduba.us,jianvan.com,rammohila.org,adylife.us,blatota.us,jxh.us,xinian.fun,wendin.fun,belayet.org,weigeniu.cc,ku.youngesta.org,tktok.cc,ddip88.com,kazuto123.com,kazuto1.com,kousei00.com,kousei.com,kousei2.com,haoniua.cc,fadacaia.com,hongyunyj.com,bufeng666.com,guanlig.cc,becausean.com,outsidevau.com,zhiwangtu.cc,lndfe.life,tfgzs.com,kgnce.life,diankaot.run,cotn.uk,fewne.life,youngesta.org,tkiii.vip,changebt.com,caokaf.online,eoqjjqg.com,keyboard.run,nbvidapp.cc,exiannvk.com,xintz.fun,butingquan.com,nullsto.edu.pl,mailto.plus,chitthi.in,rover.info,mailbox.in.ua,fexpost.com,fexbox.org,fextemp.com,bkle.uk,kazuto123.com,kazuto1.com,kousei00.com,kousei2.com,kousei.com,hahzo.com,merepost.com,edny.net,any.pink,dis.hopto.org,emaily.pro,lyx13.xyz,schooll.chat,zodiacalgf.com,kaixinhen.com,as.grandmada.org,involvedop.com,sciencekg.com,clulu.fun,beduo.fun,eemm.online,shunli.online,documenit.com,sanyu.online,duolun.xyz,bnsteps.com,apklamp.com,egvoo.com,calmpros.com,commentsf.com,arinuse.com,mxl001.win,442587.xyz,51788.top,tmmad.com,zvvzuv.com,wywnxa.com,x866.cc,cra335.site,qwer.usphms.shop,dapiao.online,ancd.us"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "BANNED_IP_SOURCES": {"name": "BANNED_IP_SOURCES", "type": "release", "description": "Banned source IP hashes, currently used for fighting spammers.", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "BANNED_IP_SOURCES", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "banned", "payload": {"type": "string", "value": "33750fa8a94af0a452d2d30bad9e944fa569e112b58ba911e8d60b206282e937,5795b0391560d0bc128169ae18c9966b1304c27db3066bed4716261f89151<PERSON>,eb5acc865f3bb9f0c2c09b73875587dca11a6c802242ea15f20187b7af9717d2,20e92056a1d135ce8946ead78f64a897d8598194edf94d9175c23d4a3e855334,30859ff2e79feefe9a4bc7928dc9856fccbf7def5cbebe28f8ca0c9cf2d76527,ae59f6b6fbc480f226011071a80dc7cf736abb2a4c38b653dcc20ff0c3fcf24e,dafa1290ed88b6333824c0536c553b3f80bd72c9c0c12f9ede18a115aacd123b,5061518da28cd4ceca7721a291658cb8a81df6557ac26ce7a539114c70b86825,f9cb8cac50da7d5650719c48830907badf3330e7d1b745ea615cd246b30df687,2fff58be99f209eafbb2d43066365a1bf791c0c4986940b233f159d1225246ec,dd87f9425894c41b8fab77dc92951d791bd910fd4cf0a68463192025c7523f99,9319cbe98d571b8a3c11dfb4414c785e88694fd144048e60329cc0b862a522ed,**************,*************,**************,************,**************,95.216.45.233,43.134.17.224,95.217.72.181,43.134.12.154,194.36.171.49,119.28.119.172,43.153.195.56,85.194.243.117,91.103.121.31,138.201.82.55,199.245.101.143,198.56.6.108,104.194.80.193,149.88.90.176,45.134.107.27,136.227.183.162,45.84.80.16,45.143.175.113,134.199.78.221,194.187.37.144,154.30.99.146,134.199.75.67,136.227.181.96,45.88.100.36,136.227.168.216,136.227.190.22,134.199.80.223,204.217.149.135,5.253.185.239,178.171.117.81,178.171.117.81,88.214.3.213,136.227.170.191,195.133.209.198,***********,**********,************,*************,***************,************,*************,**************,**************,**************"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "BLOCK_TAB_ON_SHOWN_AUTOCOMPLETE": {"name": "BLOCK_TAB_ON_SHOWN_AUTOCOMPLETE", "type": "release", "description": "", "enabled": false, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-insiders"], "value": "", "caseInsensitive": false, "inverted": false}, {"contextName": "ide", "operator": "IN", "values": ["windsurf-next"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "BLOCK_TAB_ON_SHOWN_AUTOCOMPLETE", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "CASCADE_ADD_TO_IMPLICIT_TRAJECTORY": {"name": "CASCADE_ADD_TO_IMPLICIT_TRAJECTORY", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-insiders"], "value": "", "caseInsensitive": false, "inverted": false}, {"contextName": "ideVersion", "operator": "SEMVER_LT", "values": [], "value": "10.0.7", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "CASCADE_ADD_TO_IMPLICIT_TRAJECTORY", "rollout": "100", "stickiness": "sessionId"}, "segments": null, "variants": []}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-next"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "CASCADE_ADD_TO_IMPLICIT_TRAJECTORY", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "CASCADE_AUTO_FIX_LINTS": {"name": "CASCADE_AUTO_FIX_LINTS", "type": "experiment", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-insiders"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "CASCADE_AUTO_FIX_LINTS", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-next", "windsurf"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "CASCADE_AUTO_FIX_LINTS", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "encourage_giving_up_if_many_lint_steps", "payload": {"type": "string", "value": "As IDE feedback, the following lint errors may be related to your recent edits up to this point. Consider whether they deserve immediate attention. If worth addressing, clearly comment on them and/or fix them. Be explicit in acknowledging lints and explaining your fix's approach. AVOID unproductive loops; if you detect yourself repeatedly creating/fixing lints in a short period, offer some thoughts but MOVE ON."}, "enabled": false, "featureEnabled": false, "weight": 600, "weightType": "", "stickiness": "default", "overrides": null}, {"name": "original_prompt", "payload": {"type": "string", "value": "As IDE feedback, the following lint errors may be related to your recent edits up to this point. Consider whether they deserve immediate attention. If worth addressing, clearly comment on them and/or fix them. Try to be explicit, whether that's explaining how you'll fix them, noting that you'll revisit them, or acknowledging if you don't know how to resolve them. Do try to fix them if you can, but exercise prudence; try not to create extra errors."}, "enabled": false, "featureEnabled": false, "weight": 400, "weightType": "", "stickiness": "default", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "CASCADE_BACKGROUND_CASCADE_NUX": {"name": "CASCADE_BACKGROUND_CASCADE_NUX", "type": "release", "description": "Feature flag for the background local Cascade NUX", "enabled": false, "strategies": [], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "CASCADE_BACKGROUND_RESEARCH_CONFIG_OVERRIDE": {"name": "CASCADE_BACKGROUND_RESEARCH_CONFIG_OVERRIDE", "type": "release", "description": "", "enabled": false, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "CASCADE_BACKGROUND_RESEARCH_CONFIG_OVERRIDE", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "4oMini_40K", "payload": {"type": "json", "value": "{\n  \"planner_config\": {\n    \"plan_model\": \"MODEL_CHAT_GPT_4O_MINI_2024_07_18\"\n  },\n  \"checkpoint_config\": {\n    \"token_threshold\": \"30000\",\n    \"max_overhead_ratio\": \"0.05\",\n    \"moving_window_size\": \"1\",\n    \"max_token_limit\": \"40000\",\n    \"enabled\": true\n  }\n}"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "CASCADE_BASE_MODEL_ID": {"name": "CASCADE_BASE_MODEL_ID", "type": "release", "description": "This defines the model that we map to \"CASCADE_FREE\" model option in the extension.\n\nYou should never disable this experiment.", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ideVersion", "operator": "SEMVER_LT", "values": [], "value": "1.5.6", "caseInsensitive": false, "inverted": true}], "parameters": {"groupId": "CASCADE_BASE_MODEL_ID", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "MODEL_CASCADE_20068", "payload": {"type": "string", "value": "MODEL_CASCADE_20068"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "CASCADE_BASE_MODEL_ID", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "4O_mini", "payload": {"type": "string", "value": "MODEL_CHAT_GPT_4O_MINI_2024_07_18"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}, {"name": "HAIKU_40K", "payload": {"type": "string", "value": "MODEL_CLAUDE_3_5_HAIKU_20241022"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "default", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "CASCADE_DEEPSEEK_R1_ACCESS": {"name": "CASCADE_DEEPSEEK_R1_ACCESS", "type": "release", "description": "", "enabled": false, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "CASCADE_DEEPSEEK_R1_ACCESS", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "CASCADE_DEEPSEEK_V3_ACCESS": {"name": "CASCADE_DEEPSEEK_V3_ACCESS", "type": "release", "description": "", "enabled": false, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "CASCADE_DEEPSEEK_V3_ACCESS", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "CASCADE_DEFAULT_MODEL_OVERRIDE": {"name": "CASCADE_DEFAULT_MODEL_OVERRIDE", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-next"], "value": "", "caseInsensitive": false, "inverted": false}, {"contextName": "ideVersion", "operator": "SEMVER_LT", "values": [], "value": "1.9.104", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "CASCADE_DEFAULT_MODEL_OVERRIDE", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "MODEL_GOOGLE_GEMINI_2_5_PRO", "payload": {"type": "string", "value": "MODEL_GOOGLE_GEMINI_2_5_PRO"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-insiders"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "CASCADE_DEFAULT_MODEL_OVERRIDE", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "MODEL_GOOGLE_GEMINI_2_5_PRO", "payload": {"type": "string", "value": "MODEL_GOOGLE_GEMINI_2_5_PRO"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf"], "value": "", "caseInsensitive": false, "inverted": false}, {"contextName": "ideVersion", "operator": "SEMVER_LT", "values": [], "value": "1.9.4", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "CASCADE_DEFAULT_MODEL_OVERRIDE", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "MODEL_GOOGLE_GEMINI_2_5_PRO", "payload": {"type": "string", "value": "MODEL_GOOGLE_GEMINI_2_5_PRO"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "CASCADE_DEPLOYMENTS_TOOLBAR_VISIBILITY": {"name": "CASCADE_DEPLOYMENTS_TOOLBAR_VISIBILITY", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "devMode", "operator": "IN", "values": ["true"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "CASCADE_DEPLOYMENTS_TOOLBAR_VISIBILITY", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "CASCADE_ENABLE_AUTOMATED_MEMORIES": {"name": "CASCADE_ENABLE_AUTOMATED_MEMORIES", "type": "experiment", "description": "Whether to give <PERSON> the ability to create, update, and delete its own memories.", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-insiders"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "CASCADE_ENABLE_AUTOMATED_MEMORIES", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-next"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "CASCADE_ENABLE_AUTOMATED_MEMORIES", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf"], "value": "", "caseInsensitive": false, "inverted": false}, {"contextName": "requestedModelId", "operator": "IN", "values": ["MODEL_CLAUDE_3_7_SONNET_20250219_THINKING", "MODEL_CLAUDE_3_7_SONNET_20250219"], "value": "", "caseInsensitive": false, "inverted": true}], "parameters": {"groupId": "CASCADE_ENABLE_AUTOMATED_MEMORIES", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf"], "value": "", "caseInsensitive": false, "inverted": false}, {"contextName": "requestedModelId", "operator": "IN", "values": ["MODEL_CLAUDE_3_7_SONNET_20250219", "MODEL_CLAUDE_3_7_SONNET_20250219_THINKING"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "CASCADE_ENABLE_AUTOMATED_MEMORIES", "rollout": "50", "stickiness": "default"}, "segments": null, "variants": []}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["jetbrains"], "value": "", "caseInsensitive": false, "inverted": false}, {"contextName": "ideVersion", "operator": "IN", "values": ["2.1.0", "2.0.0"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "CASCADE_ENABLE_AUTOMATED_MEMORIES", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["jetbrains"], "value": "", "caseInsensitive": false, "inverted": false}, {"contextName": "ideVersion", "operator": "SEMVER_LT", "values": [], "value": "2.0.0", "caseInsensitive": false, "inverted": true}], "parameters": {"groupId": "CASCADE_ENABLE_AUTOMATED_MEMORIES", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "CASCADE_ENABLE_CUSTOM_RECIPES": {"name": "CASCADE_ENABLE_CUSTOM_RECIPES", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "devMode", "operator": "IN", "values": ["true"], "value": "", "caseInsensitive": false, "inverted": false}, {"contextName": "ide", "operator": "IN", "values": ["jetbrains"], "value": "", "caseInsensitive": false, "inverted": true}], "parameters": {"groupId": "CASCADE_ENABLE_CUSTOM_RECIPES", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "CASCADE_ENABLE_IDE_TERMINAL_EXECUTION": {"name": "CASCADE_ENABLE_IDE_TERMINAL_EXECUTION", "type": "experiment", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "devMode", "operator": "IN", "values": ["true"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "CASCADE_ENABLE_IDE_TERMINAL_EXECUTION", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "extensionVersion", "operator": "SEMVER_GT", "values": [], "value": "1.32.1", "caseInsensitive": false, "inverted": false}, {"contextName": "ide", "operator": "STR_CONTAINS", "values": ["windsurf"], "value": "", "caseInsensitive": true, "inverted": false}], "parameters": {"groupId": "CASCADE_ENABLE_IDE_TERMINAL_EXECUTION", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "CASCADE_ENABLE_MCP_TOOLS": {"name": "CASCADE_ENABLE_MCP_TOOLS", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "devMode", "operator": "IN", "values": ["true"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "CASCADE_ENABLE_MCP_TOOLS", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-next"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "CASCADE_ENABLE_MCP_TOOLS", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf"], "value": "", "caseInsensitive": false, "inverted": false}, {"contextName": "ideVersion", "operator": "SEMVER_GT", "values": [], "value": "1.3.0", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "CASCADE_ENABLE_MCP_TOOLS", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-insiders"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "CASCADE_ENABLE_MCP_TOOLS", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["jetbrains"], "value": "", "caseInsensitive": false, "inverted": false}, {"contextName": "ideVersion", "operator": "SEMVER_LT", "values": [], "value": "2.0.0", "caseInsensitive": false, "inverted": true}], "parameters": {"groupId": "CASCADE_ENABLE_MCP_TOOLS", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "CASCADE_ENABLE_PROXY_WEB_SERVER": {"name": "CASCADE_ENABLE_PROXY_WEB_SERVER", "type": "experiment", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["jetbrains"], "value": "", "caseInsensitive": false, "inverted": true}], "parameters": {"groupId": "CASCADE_ENABLE_PROXY_WEB_SERVER", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "CASCADE_ENFORCE_QUOTA": {"name": "CASCADE_ENFORCE_QUOTA", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "CASCADE_ENFORCE_QUOTA", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "devMode", "operator": "IN", "values": ["true"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "CASCADE_ENFORCE_QUOTA", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "CASCADE_FILE_OVERVIEW_POPOVER_USE_IDE_STATE": {"name": "CASCADE_FILE_OVERVIEW_POPOVER_USE_IDE_STATE", "type": "experiment", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "CASCADE_FILE_OVERVIEW_POPOVER_USE_IDE_STATE", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "CASCADE_FREE_CONFIG_OVERRIDE": {"name": "CASCADE_FREE_CONFIG_OVERRIDE", "type": "release", "description": "This is the set of config options we give to non-premium users by default.\n\nYou should never disable this experiment.", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "CASCADE_FREE_CONFIG_OVERRIDE", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "40K_LIMIT", "payload": {"type": "json", "value": "{\n  \"checkpoint_config\": {\n    \"token_threshold\": \"30000\",\n    \"max_overhead_ratio\": \"0.1\",\n    \"moving_window_size\": \"1\",\n    \"max_token_limit\": \"45000\",\n    \"enabled\": true,\n    \"checkpoint_model\": \"MODEL_CHAT_GPT_4O_MINI_2024_07_18\"\n  },\n  \"planner_config\": {\n    \"max_output_tokens\": \"8192\"\n  }\n}"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "CASCADE_GLOBAL_CONFIG_OVERRIDE": {"name": "CASCADE_GLOBAL_CONFIG_OVERRIDE", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "requestedModelId", "operator": "IN", "values": ["MODEL_CLAUDE_3_7_SONNET_20250219", "MODEL_CLAUDE_3_7_SONNET_20250219_THINKING"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "CASCADE_GLOBAL_CONFIG_OVERRIDE", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "ephemeral", "payload": {"type": "json", "value": "{\n    \"planner_config\": {\n        \"include_ephemeral_message\": true,\n        \"tool_config\": {\n            \"run_command\": {\n                \"auto_command_config\": {\n                    \"system_allowlist\": [\n                        \"echo\",\n                        \"ls\"\n                    ],\n                    \"system_denylist\": [\n                        \"git\",\n                        \"rm\",\n                        \"pkill\",\n                        \"kubectl delete\",\n                        \"kubectl apply\",\n                        \"terraform\",\n                        \"kill\",\n                        \"del\",\n                        \"rmdir\",\n                        \"psql\",\n                        \"mv\",\n                        \"bash\",\n                        \"zsh\"\n                    ]\n                }\n            }\n        }\n    }\n}"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "cascadeModelAlias", "operator": "IN", "values": ["MODEL_ALIAS_CASCADE_BASE", "MODEL_ALIAS_VISTA", "MODEL_ALIAS_SWE_1", "MODEL_ALIAS_SWE_1_LITE", "MODEL_ALIAS_SHAMU"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "CASCADE_GLOBAL_CONFIG_OVERRIDE", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "ephemeral", "payload": {"type": "json", "value": "{\n    \"memory_config\": {\n        \"add_user_memories_to_system_prompt\": false\n    },\n    \"planner_config\": {\n        \"include_ephemeral_message\": false,\n        \"tool_config\": {\n            \"run_command\": {\n                \"auto_command_config\": {\n                    \"system_allowlist\": [\n                        \"echo\",\n                        \"ls\"\n                    ],\n                    \"system_denylist\": [\n                        \"git\",\n                        \"rm\",\n                        \"pkill\",\n                        \"kubectl delete\",\n                        \"kubectl apply\",\n                        \"terraform\",\n                        \"kill\",\n                        \"del\",\n                        \"rmdir\",\n                        \"psql\",\n                        \"mv\",\n                        \"bash\",\n                        \"zsh\"\n                    ]\n                }\n            }\n        }\n    }\n}"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "default", "overrides": null}, {"name": "no_memory", "payload": {"type": "json", "value": "{\n    \"memory_config\": {\n        \"add_user_memories_to_system_prompt\": false\n    },\n    \"planner_config\": {\n        \"include_ephemeral_message\": false,\n        \"tool_config\": {\n            \"run_command\": {\n                \"auto_command_config\": {\n                    \"system_allowlist\": [\n                        \"echo\",\n                        \"ls\"\n                    ],\n                    \"system_denylist\": [\n                        \"git\",\n                        \"rm\",\n                        \"pkill\",\n                        \"kubectl delete\",\n                        \"kubectl apply\",\n                        \"terraform\",\n                        \"kill\",\n                        \"del\",\n                        \"rmdir\",\n                        \"psql\",\n                        \"mv\",\n                        \"bash\",\n                        \"zsh\"\n                    ]\n                }\n            }\n        }\n    }\n}"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "CASCADE_GLOBAL_CONFIG_OVERRIDE", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "default", "payload": {"type": "json", "value": "{\n    \"planner_config\": {\n        \"tool_config\": {\n            \"run_command\": {\n                \"auto_command_config\": {\n                    \"system_allowlist\": [\n                        \"echo\",\n                        \"ls\"\n                    ],\n                    \"system_denylist\": [\n                        \"git\",\n                        \"rm\",\n                        \"pkill\",\n                        \"kubectl delete\",\n                        \"kubectl apply\",\n                        \"terraform\",\n                        \"kill\",\n                        \"del\",\n                        \"rmdir\",\n                        \"psql\",\n                        \"mv\",\n                        \"bash\",\n                        \"zsh\"\n                    ]\n                }\n            }\n        }\n    }\n}"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "default", "overrides": null}, {"name": "no-ephemeral", "payload": {"type": "json", "value": "{\n    \"planner_config\": {\n        \"include_ephemeral_message\": false,\n        \"tool_config\": {\n            \"run_command\": {\n                \"auto_command_config\": {\n                    \"system_allowlist\": [\n                        \"echo\",\n                        \"ls\"\n                    ],\n                    \"system_denylist\": [\n                        \"git\",\n                        \"rm\",\n                        \"pkill\",\n                        \"kubectl delete\",\n                        \"kubectl apply\",\n                        \"terraform\",\n                        \"kill\",\n                        \"del\",\n                        \"rmdir\",\n                        \"psql\",\n                        \"mv\",\n                        \"bash\",\n                        \"zsh\"\n                    ]\n                }\n            }\n        }\n    }\n}"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "CASCADE_MEMORY_CONFIG_OVERRIDE": {"name": "CASCADE_MEMORY_CONFIG_OVERRIDE", "type": "release", "description": "A release toggle to configure global memory configs", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-insiders"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "CASCADE_MEMORY_CONFIG_OVERRIDE", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "default", "payload": {"type": "json", "value": "{\n  \"memory_model\": \"MODEL_CHAT_GPT_4O_MINI_2024_07_18\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-next"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "CASCADE_MEMORY_CONFIG_OVERRIDE", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "windsurf-next", "payload": {"type": "json", "value": "{\n  \"memory_model\": \"MODEL_CHAT_GPT_4O_MINI_2024_07_18\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "CASCADE_MEMORY_CONFIG_OVERRIDE", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "default", "payload": {"type": "json", "value": "{\n  \"memory_model\": \"MODEL_CHAT_GPT_4O_MINI_2024_07_18\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "CASCADE_MODEL_HEADER_WARNING": {"name": "CASCADE_MODEL_HEADER_WARNING", "type": "release", "description": "", "enabled": false, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "CASCADE_MODEL_HEADER_WARNING", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "CASCADE_NEW_MODELS_NUX": {"name": "CASCADE_NEW_MODELS_NUX", "type": "release", "description": "", "enabled": false, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "CASCADE_NEW_MODELS_NUX", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "CASCADE_NEW_WAVE_2_MODELS_NUX": {"name": "CASCADE_NEW_WAVE_2_MODELS_NUX", "type": "release", "description": "", "enabled": false, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "devMode", "operator": "IN", "values": ["true"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "CASCADE_NEW_WAVE_2_MODELS_NUX", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "CASCADE_ONBOARDING": {"name": "CASCADE_ONBOARDING", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-next"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "CASCADE_ONBOARDING", "rollout": "50", "stickiness": "default"}, "segments": null, "variants": []}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "planName", "operator": "IN", "values": ["Free", "Trial"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "CASCADE_ONBOARDING", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "CASCADE_ONBOARDING_REVERT": {"name": "CASCADE_ONBOARDING_REVERT", "type": "release", "description": "Flag to hide/show the NUX for revert", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-next"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "CASCADE_ONBOARDING_REVERT", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}, {"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "CASCADE_ONBOARDING_REVERT", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "CASCADE_OPENAI_O3_MINI_ACCESS": {"name": "CASCADE_OPENAI_O3_MINI_ACCESS", "type": "release", "description": "", "enabled": false, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "CASCADE_OPENAI_O3_MINI_ACCESS", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "CASCADE_PLAN_BASED_CONFIG_OVERRIDE": {"name": "CASCADE_PLAN_BASED_CONFIG_OVERRIDE", "type": "release", "description": "WARNING: \n\nIt is very easy to set incompatible set of override values that will immediately cause errors. If you change these settings, you must monitor sentry for config validation errors.\n\nKnown issues:\n- If checkpoint_config.TokenThreshold <= 3 * checkpoint_config.MaxOutputTokens\n- Newly added config fields are not safely compatible with older versions. If you added a field, you cannot specify it without also specifying a target version that is >= version which added MaxOutputTokens", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "devMode", "operator": "IN", "values": ["true"], "value": "", "caseInsensitive": false, "inverted": false}, {"contextName": "requestedModelId", "operator": "IN", "values": ["MODEL_CHAT_O4_MINI_HIGH"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "CASCADE_PLAN_BASED_CONFIG_OVERRIDE", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "enterprise", "payload": {"type": "json", "value": "{\n  \"checkpoint_config\": {\n    \"max_token_limit\": \"135000\",\n    \"checkpoint_model\": \"MODEL_CLAUDE_3_5_HAIKU_20241022\"\n  },\n  \"planner_config\": {\n    \"max_output_tokens\": \"32768\"\n  }\n}"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "default", "overrides": null}, {"name": "saas", "payload": {"type": "json", "value": "\n{\n  \"checkpoint_config\": {\n    \"max_token_limit\": \"45000\"\n  },\n  \"planner_config\": {\n    \"max_output_tokens\": \"32768\"\n  }\n}"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "devMode", "operator": "IN", "values": ["true"], "value": "", "caseInsensitive": false, "inverted": false}, {"contextName": "requestedModelId", "operator": "IN", "values": ["MODEL_GOOGLE_GEMINI_2_5_PRO"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "CASCADE_PLAN_BASED_CONFIG_OVERRIDE", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "enterprise", "payload": {"type": "json", "value": "{\n  \"checkpoint_config\": {\n    \"max_token_limit\": \"135000\",\n    \"checkpoint_model\": \"MODEL_CLAUDE_3_5_HAIKU_20241022\"\n  },\n  \"planner_config\": {\n    \"max_output_tokens\": \"12288\"\n  }\n}"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "default", "overrides": null}, {"name": "saas", "payload": {"type": "json", "value": "\n{\n  \"checkpoint_config\": {\n    \"max_token_limit\": \"45000\"\n  },\n  \"planner_config\": {\n    \"max_output_tokens\": \"12288\"\n  }\n}"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "devMode", "operator": "IN", "values": ["true"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "CASCADE_PLAN_BASED_CONFIG_OVERRIDE", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "enterprise", "payload": {"type": "json", "value": "{\n  \"checkpoint_config\": {\n    \"max_token_limit\": \"135000\",\n    \"checkpoint_model\": \"MODEL_CLAUDE_3_5_HAIKU_20241022\"\n  },\n  \"planner_config\": {\n    \"max_output_tokens\": \"8192\"\n  }\n}"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "default", "overrides": null}, {"name": "saas", "payload": {"type": "json", "value": "{\n  \"checkpoint_config\": {\n    \"max_token_limit\": \"45000\"\n  },\n  \"planner_config\": {\n    \"max_output_tokens\": \"8192\"\n  }\n}"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "cascadeEnterpriseMode", "operator": "IN", "values": ["true"], "value": "", "caseInsensitive": false, "inverted": false}, {"contextName": "requestedModelId", "operator": "IN", "values": ["MODEL_CHAT_O4_MINI_HIGH"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "CASCADE_PLAN_BASED_CONFIG_OVERRIDE", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "Enterprise", "payload": {"type": "json", "value": "{\n  \"checkpoint_config\": {\n    \"max_token_limit\": \"135000\",\n    \"checkpoint_model\": \"MODEL_CLAUDE_3_5_HAIKU_20241022\"\n  },\n  \"planner_config\": {\n    \"max_output_tokens\": \"32768\"\n  }\n}"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "cascadeEnterpriseMode", "operator": "IN", "values": ["true"], "value": "", "caseInsensitive": false, "inverted": false}, {"contextName": "requestedModelId", "operator": "IN", "values": ["MODEL_GOOGLE_GEMINI_2_5_PRO"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "CASCADE_PLAN_BASED_CONFIG_OVERRIDE", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "Enterprise", "payload": {"type": "json", "value": "{\n  \"checkpoint_config\": {\n    \"max_token_limit\": \"135000\",\n    \"checkpoint_model\": \"MODEL_CLAUDE_3_5_HAIKU_20241022\"\n  },\n  \"planner_config\": {\n    \"max_output_tokens\": \"12288\"\n  }\n}"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "requestedModelId", "operator": "IN", "values": ["MODEL_CLAUDE_3_7_SONNET_20250219", "MODEL_CLAUDE_3_7_SONNET_20250219_THINKING", "MODEL_CLAUDE_4_SONNET", "MODEL_CLAUDE_4_SONNET_THINKING"], "value": "", "caseInsensitive": false, "inverted": false}, {"contextName": "cascadeEnterpriseMode", "operator": "IN", "values": ["true"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "CASCADE_PLAN_BASED_CONFIG_OVERRIDE", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "Enterprise", "payload": {"type": "json", "value": "{\n  \"checkpoint_config\": {\n    \"max_token_limit\": \"135000\",\n    \"checkpoint_model\": \"MODEL_CLAUDE_3_5_HAIKU_20241022\"\n  },\n  \"planner_config\": {\n    \"max_output_tokens\": \"6144\"\n  }\n}"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "cascadeEnterpriseMode", "operator": "IN", "values": ["true"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "CASCADE_PLAN_BASED_CONFIG_OVERRIDE", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "Enterprise", "payload": {"type": "json", "value": "{\n  \"checkpoint_config\": {\n    \"max_token_limit\": \"135000\",\n    \"checkpoint_model\": \"MODEL_CLAUDE_3_5_HAIKU_20241022\"\n  },\n  \"planner_config\": {\n    \"max_output_tokens\": \"8192\"\n  }\n}"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "requestedModelId", "operator": "IN", "values": ["MODEL_CHAT_O4_MINI_HIGH"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "CASCADE_PLAN_BASED_CONFIG_OVERRIDE", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "default", "payload": {"type": "json", "value": "{\n  \"checkpoint_config\": {\n    \"max_token_limit\": \"45000\"\n  },\n  \"planner_config\": {\n    \"max_output_tokens\": \"32768\",\n    \"truncation_threshold_tokens\": \"100000\"\n  }\n}"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "requestedModelId", "operator": "IN", "values": ["MODEL_GOOGLE_GEMINI_2_5_PRO"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "CASCADE_PLAN_BASED_CONFIG_OVERRIDE", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "default", "payload": {"type": "json", "value": "{\n  \"checkpoint_config\": {\n    \"max_token_limit\": \"45000\"\n  },\n  \"planner_config\": {\n    \"max_output_tokens\": \"12288\",\n    \"truncation_threshold_tokens\": \"100000\"\n  }\n}"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "requestedModelId", "operator": "IN", "values": ["MODEL_CLAUDE_4_SONNET", "MODEL_CLAUDE_3_7_SONNET_20250219", "MODEL_CLAUDE_3_7_SONNET_20250219_THINKING", "MODEL_CLAUDE_4_SONNET_THINKING"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "CASCADE_PLAN_BASED_CONFIG_OVERRIDE", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "default", "payload": {"type": "json", "value": "{ \n  \"checkpoint_config\": { \n    \"max_token_limit\": \"45000\" \n  },\n  \"planner_config\": { \n    \"max_output_tokens\": \"6144\",\n    \"truncation_threshold_tokens\": \"100000\" \n  } \n}"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "CASCADE_PLAN_BASED_CONFIG_OVERRIDE", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "default", "payload": {"type": "json", "value": "{\n  \"checkpoint_config\": {\n    \"max_token_limit\": \"45000\"\n  },\n  \"planner_config\": {\n    \"max_output_tokens\": \"8192\",\n    \"truncation_threshold_tokens\": \"100000\"\n  }\n}"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "CASCADE_PLUGINS_TAB": {"name": "CASCADE_PLUGINS_TAB", "type": "release", "description": "If enabled, shows the plugins tab in Cascade.", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-insiders"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "CASCADE_PLUGINS_TAB", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-next"], "value": "", "caseInsensitive": false, "inverted": false}, {"contextName": "ideVersion", "operator": "SEMVER_GT", "values": [], "value": "1.8.100", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "CASCADE_PLUGINS_TAB", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf"], "value": "", "caseInsensitive": false, "inverted": false}, {"contextName": "ideVersion", "operator": "SEMVER_LT", "values": [], "value": "1.8.0", "caseInsensitive": false, "inverted": true}], "parameters": {"groupId": "CASCADE_PLUGINS_TAB", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "CASCADE_PREMIUM_CONFIG_OVERRIDE": {"name": "CASCADE_PREMIUM_CONFIG_OVERRIDE", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "CASCADE_PREMIUM_CONFIG_OVERRIDE", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "40k_haiku", "payload": {"type": "json", "value": "{\n  \"checkpoint_config\": {\n    \"token_threshold\": \"30000\",\n    \"max_overhead_ratio\": \"0.1\",\n    \"moving_window_size\": \"1\",\n    \"max_token_limit\": \"40000\",\n    \"enabled\": true,\n    \"checkpoint_model\": \"MODEL_CLAUDE_3_5_HAIKU_20241022\"\n  }\n}"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "default", "overrides": null}, {"name": "40k_limit", "payload": {"type": "json", "value": "{\n  \"checkpoint_config\": {\n    \"token_threshold\": \"30000\",\n    \"max_overhead_ratio\": \"0.1\",\n    \"moving_window_size\": \"1\",\n    \"max_token_limit\": \"45000\",\n    \"enabled\": true,\n    \"checkpoint_model\": \"MODEL_CHAT_GPT_4O_MINI_2024_07_18\"\n  },\n  \"planner_config\": {\n    \"max_output_tokens\": \"8192\"\n  }\n}"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "CASCADE_RECIPES_AT_MENTION_VISIBILITY": {"name": "CASCADE_RECIPES_AT_MENTION_VISIBILITY", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "devMode", "operator": "IN", "values": ["true"], "value": "", "caseInsensitive": false, "inverted": false}, {"contextName": "ide", "operator": "IN", "values": ["jetbrains"], "value": "", "caseInsensitive": false, "inverted": true}], "parameters": {"groupId": "CASCADE_RECIPES_AT_MENTION_VISIBILITY", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-next", "windsurf-insiders", "windsurf"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "CASCADE_RECIPES_AT_MENTION_VISIBILITY", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "CASCADE_RULES_NUX_COPY": {"name": "CASCADE_RULES_NUX_COPY", "type": "release", "description": "", "enabled": false, "strategies": [], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "CASCADE_TOOL_CALL_NUX": {"name": "CASCADE_TOOL_CALL_NUX", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "devMode", "operator": "IN", "values": ["true"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "CASCADE_TOOL_CALL_NUX", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "CASCADE_TOOL_CALL_PRICING_NUX": {"name": "CASCADE_TOOL_CALL_PRICING_NUX", "type": "release", "description": "", "enabled": false, "strategies": [], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "CASCADE_TRIAL_DEFAULT_MODEL_OVERRIDE": {"name": "CASCADE_TRIAL_DEFAULT_MODEL_OVERRIDE", "type": "release", "description": "Overrides the default model for trial users only.", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-next"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "CASCADE_TRIAL_DEFAULT_MODEL_OVERRIDE", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "gemini25", "payload": {"type": "string", "value": "MODEL_GOOGLE_GEMINI_2_5_PRO"}, "enabled": false, "featureEnabled": false, "weight": 334, "weightType": "", "stickiness": "default", "overrides": null}, {"name": "GPT41", "payload": {"type": "string", "value": "MODEL_CHAT_GPT_4_1_2025_04_14"}, "enabled": false, "featureEnabled": false, "weight": 333, "weightType": "", "stickiness": "default", "overrides": null}, {"name": "o3", "payload": {"type": "string", "value": "MODEL_CHAT_O3"}, "enabled": false, "featureEnabled": false, "weight": 333, "weightType": "", "stickiness": "default", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "CASCADE_TRIAL_DEFAULT_MODEL_OVERRIDE", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "gemini25", "payload": {"type": "string", "value": "MODEL_GOOGLE_GEMINI_2_5_PRO"}, "enabled": false, "featureEnabled": false, "weight": 334, "weightType": "", "stickiness": "default", "overrides": null}, {"name": "GPT41", "payload": {"type": "string", "value": "MODEL_CHAT_GPT_4_1_2025_04_14"}, "enabled": false, "featureEnabled": false, "weight": 333, "weightType": "", "stickiness": "default", "overrides": null}, {"name": "o3", "payload": {"type": "string", "value": "MODEL_CHAT_O3"}, "enabled": false, "featureEnabled": false, "weight": 333, "weightType": "", "stickiness": "default", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "CASCADE_USER_MEMORIES_IN_SYS_PROMPT": {"name": "CASCADE_USER_MEMORIES_IN_SYS_PROMPT", "type": "experiment", "description": "Experiment to inject the user memories directly into the system prompt", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-insiders"], "value": "", "caseInsensitive": false, "inverted": false}, {"contextName": "ideVersion", "operator": "SEMVER_LT", "values": [], "value": "10.0.7", "caseInsensitive": false, "inverted": true}], "parameters": {"groupId": "CASCADE_USER_MEMORIES_IN_SYS_PROMPT", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "system_prompt", "payload": {"type": "json", "value": "{\n  \"add_user_memories_to_system_prompt\": true\n}\n"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "cascadeModelAlias", "operator": "IN", "values": ["MODEL_ALIAS_CASCADE_BASE", "MODEL_ALIAS_VISTA"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "CASCADE_USER_MEMORIES_IN_SYS_PROMPT", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "system_prompt", "payload": {"type": "json", "value": "{\n  \"add_user_memories_to_system_prompt\": false\n}\n"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-next"], "value": "", "caseInsensitive": false, "inverted": false}, {"contextName": "ideVersion", "operator": "SEMVER_LT", "values": [], "value": "1.5.107", "caseInsensitive": false, "inverted": true}], "parameters": {"groupId": "CASCADE_USER_MEMORIES_IN_SYS_PROMPT", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "in_system_prompt", "payload": {"type": "json", "value": "{\n  \"add_user_memories_to_system_prompt\": true\n}\n"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf"], "value": "", "caseInsensitive": false, "inverted": false}, {"contextName": "ideVersion", "operator": "SEMVER_LT", "values": [], "value": "1.3.10", "caseInsensitive": false, "inverted": true}], "parameters": {"groupId": "CASCADE_USER_MEMORIES_IN_SYS_PROMPT", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "in_system_prompt", "payload": {"type": "json", "value": "{\n  \"add_user_memories_to_system_prompt\": true\n}\n"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "CASCADE_USE_EVERGREEN_TOOLBAR": {"name": "CASCADE_USE_EVERGREEN_TOOLBAR", "type": "experiment", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-next", "windsurf-insiders"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "CASCADE_USE_EVERGREEN_TOOLBAR", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "devMode", "operator": "IN", "values": ["true"], "value": "", "caseInsensitive": false, "inverted": false}, {"contextName": "ide", "operator": "IN", "values": ["jetbrains"], "value": "", "caseInsensitive": false, "inverted": true}], "parameters": {"groupId": "CASCADE_USE_EVERGREEN_TOOLBAR", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf"], "value": "", "caseInsensitive": false, "inverted": false}, {"contextName": "ideVersion", "operator": "SEMVER_GT", "values": [], "value": "1.3.0", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "CASCADE_USE_EVERGREEN_TOOLBAR", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "CASCADE_USE_EXPERIMENT_CHECKPOINTER": {"name": "CASCADE_USE_EXPERIMENT_CHECKPOINTER", "type": "experiment", "description": "A flag for online checkpoint experiments", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "requestedModelId", "operator": "IN", "values": ["MODEL_CLAUDE_4_SONNET", "MODEL_CLAUDE_4_SONNET_THINKING"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "CASCADE_USE_EXPERIMENT_CHECKPOINTER", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "100k", "payload": {"type": "json", "value": "{ \n  \"max_token_limit\": \"100000\",\n  \"token_threshold\": \"85000\",\n  \"max_overhead_ratio\": \"0.1\",\n  \"moving_window_size\": \"1\",\n  \"enabled\": true,\n  \"checkpoint_model\": \"MODEL_CHAT_GPT_4_1_MINI_2025_04_14\",\n  \"type\": \"CHECKPOINT_TYPE_EXPERIMENT\" }"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "CASCADE_USE_EXPERIMENT_CHECKPOINTER", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "default", "payload": {"type": "json", "value": "{   \"token_threshold\": \"30000\",   \"max_overhead_ratio\": \"0.1\",   \"moving_window_size\": \"1\",   \"enabled\": true,   \"checkpoint_model\": \"MODEL_CHAT_GPT_4_1_MINI_2025_04_14\",   \"type\": \"CHECKPOINT_TYPE_EXPERIMENT\" }"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "devMode", "operator": "IN", "values": ["true"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "CASCADE_USE_EXPERIMENT_CHECKPOINTER", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "default", "payload": {"type": "json", "value": "{\n  \"token_threshold\": \"30000\",\n  \"max_overhead_ratio\": \"0.1\",\n  \"moving_window_size\": \"1\",\n  \"enabled\": true,\n  \"checkpoint_model\": \"MODEL_CHAT_GPT_4_1_MINI_2025_04_14\",\n  \"type\": \"CHECKPOINT_TYPE_EXPERIMENT\"\n}\n"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "cascadeEnterpriseMode", "operator": "IN", "values": ["true"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "CASCADE_USE_EXPERIMENT_CHECKPOINTER", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "Hai<PERSON>", "payload": {"type": "json", "value": "{\n  \"token_threshold\": \"30000\",\n  \"max_overhead_ratio\": \"0.1\",\n  \"moving_window_size\": \"2\",\n  \"enabled\": true,\n  \"checkpoint_model\": \"MODEL_CLAUDE_3_5_HAIKU_20241022\",\n  \"type\": \"CHECKPOINT_TYPE_EXPERIMENT\"\n}\n"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "autoCascadeMode", "operator": "IN", "values": ["true"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "CASCADE_USE_EXPERIMENT_CHECKPOINTER", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "experiment_handler_mvs2", "payload": {"type": "json", "value": "{\n  \"token_threshold\": \"30000\",\n  \"max_overhead_ratio\": \"0.1\",\n  \"moving_window_size\": \"2\",\n  \"enabled\": true,\n  \"checkpoint_model\": \"MODEL_CHAT_GPT_4O_MINI_2024_07_18\",\n  \"type\": \"CHECKPOINT_TYPE_EXPERIMENT\"\n}\n"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "CASCADE_USE_PLAN_MODE": {"name": "CASCADE_USE_PLAN_MODE", "type": "release", "description": "unleash flag to control the plan mode in Cascade.", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-insiders"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "CASCADE_USE_PLAN_MODE", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-next"], "value": "", "caseInsensitive": false, "inverted": false}, {"contextName": "ideVersion", "operator": "SEMVER_GT", "values": [], "value": "1.10.101", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "CASCADE_USE_PLAN_MODE", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "CASCADE_USE_PLAN_MODE", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "CASCADE_USE_REPLACE_CONTENT_EDIT_TOOL": {"name": "CASCADE_USE_REPLACE_CONTENT_EDIT_TOOL", "type": "experiment", "description": "Whether to allow Cascade URL web document page reading", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-next"], "value": "", "caseInsensitive": false, "inverted": false}, {"contextName": "ideVersion", "operator": "SEMVER_GT", "values": [], "value": "1.8.100", "caseInsensitive": false, "inverted": false}, {"contextName": "requestedModelId", "operator": "IN", "values": ["MODEL_CHAT_GPT_4_1_2025_04_14"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "CASCADE_USE_REPLACE_CONTENT_EDIT_TOOL", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "apply_patch", "payload": {"type": "json", "value": "{\n  \"max_fuzzy_edit_distance_fraction\": \"0.001\",\n  \"allow_partial_replacement_success\": true,\n  \"fast_apply_fallback_config\": {\n    \"enabled\": true,\n    \"prompt_unchanged_threshold\": 5,\n    \"content_view_radius_lines\": 200,\n    \"content_edit_radius_lines\": 5\n  },\n  \"tool_variant\": \"REPLACE_TOOL_VARIANT_APPLY_PATCH\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 100, "weightType": "", "stickiness": "default", "overrides": null}, {"name": "default", "payload": {"type": "json", "value": "{\n  \"max_fuzzy_edit_distance_fraction\": \"0.001\",\n  \"allow_partial_replacement_success\": true,\n  \"fast_apply_fallback_config\": {\n    \"enabled\": true,\n    \"prompt_unchanged_threshold\": 5,\n    \"content_view_radius_lines\": 200,\n    \"content_edit_radius_lines\": 5\n  }\n}"}, "enabled": false, "featureEnabled": false, "weight": 900, "weightType": "", "stickiness": "default", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-insiders"], "value": "", "caseInsensitive": false, "inverted": false}, {"contextName": "extensionVersion", "operator": "SEMVER_GT", "values": [], "value": "1.34.3", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "CASCADE_USE_REPLACE_CONTENT_EDIT_TOOL", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "base", "payload": {"type": "json", "value": "{\n  \"max_fuzzy_edit_distance_fraction\": \"0.001\",\n  \"allow_partial_replacement_success\": true,\n  \"fast_apply_fallback_config\": {\n    \"enabled\": true,\n    \"prompt_unchanged_threshold\": 5,\n    \"content_view_radius_lines\": 200,\n    \"content_edit_radius_lines\": 5\n  }\n}"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf", "windsurf-next"], "value": "", "caseInsensitive": false, "inverted": false}, {"contextName": "ideVersion", "operator": "SEMVER_LT", "values": [], "value": "1.6.0", "caseInsensitive": false, "inverted": true}, {"contextName": "requestedModelId", "operator": "IN", "values": ["MODEL_CLAUDE_3_5_SONNET_20241022", "MODEL_CLAUDE_3_7_SONNET_20250219", "MODEL_CLAUDE_3_7_SONNET_20250219_THINKING", "MODEL_PRIVATE_3", "MODEL_CHAT_GPT_4_1_2025_04_14", "MODEL_GOOGLE_GEMINI_2_5_PRO", "MODEL_GOOGLE_GEMINI_2_5_FLASH_PREVIEW_05_20", "MODEL_GOOGLE_GEMINI_2_5_FLASH_PREVIEW_05_20_THINKING", "MODEL_CHAT_O3", "MODEL_CHAT_O3_HIGH", "MODEL_CHAT_O4_MINI", "MODEL_CHAT_O4_MINI_HIGH"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "CASCADE_USE_REPLACE_CONTENT_EDIT_TOOL", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "fallback", "payload": {"type": "json", "value": "{\n  \"max_fuzzy_edit_distance_fraction\": \"0.001\",\n  \"allow_partial_replacement_success\": true,\n  \"fast_apply_fallback_config\": {\n    \"enabled\": true,\n    \"prompt_unchanged_threshold\": 5,\n    \"content_view_radius_lines\": 200,\n    \"content_edit_radius_lines\": 5\n  }\n}"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}, {"name": "no-fallback", "payload": {"type": "json", "value": "{\n  \"max_fuzzy_edit_distance_fraction\": \"0.001\",\n  \"allow_partial_replacement_success\": true\n}"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "default", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "autoCascadeMode", "operator": "IN", "values": ["true"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "CASCADE_USE_REPLACE_CONTENT_EDIT_TOOL", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "cascadeModelAlias", "operator": "IN", "values": ["MODEL_ALIAS_SWE_1", "MODEL_ALIAS_SWE_1_LITE"], "value": "", "caseInsensitive": false, "inverted": false}, {"contextName": "ideVersion", "operator": "SEMVER_LT", "values": [], "value": "1.9.0", "caseInsensitive": false, "inverted": true}], "parameters": {"groupId": "CASCADE_USE_REPLACE_CONTENT_EDIT_TOOL", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "fallback", "payload": {"type": "json", "value": "{\n  \"max_fuzzy_edit_distance_fraction\": \"0.001\",\n  \"allow_partial_replacement_success\": true,\n  \"disable_allow_multiple\": true,\n  \"fast_apply_fallback_config\": {\n    \"enabled\": true,\n    \"prompt_unchanged_threshold\": 5,\n    \"content_view_radius_lines\": 200,\n    \"content_edit_radius_lines\": 5\n  }\n}"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}, {"name": "fallback_allow_multiple", "payload": {"type": "json", "value": "{\n  \"max_fuzzy_edit_distance_fraction\": \"0.001\",\n  \"allow_partial_replacement_success\": true,\n  \"disable_allow_multiple\": false,\n  \"fast_apply_fallback_config\": {\n    \"enabled\": true,\n    \"prompt_unchanged_threshold\": 5,\n    \"content_view_radius_lines\": 200,\n    \"content_edit_radius_lines\": 5\n  }\n}"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "default", "overrides": null}, {"name": "no_fallback", "payload": {"type": "json", "value": "{\n  \"max_fuzzy_edit_distance_fraction\": \"0.001\",  \n  \"allow_partial_replacement_success\": true,\n  \"disable_allow_multiple\": true\n}"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "default", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "cascadeModelAlias", "operator": "IN", "values": ["MODEL_ALIAS_CASCADE_BASE"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "CASCADE_USE_REPLACE_CONTENT_EDIT_TOOL", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "fallback", "payload": {"type": "json", "value": "{\n  \"max_fuzzy_edit_distance_fraction\": \"0.001\",\n  \"allow_partial_replacement_success\": true,\n  \"fast_apply_fallback_config\": {\n    \"enabled\": true,\n    \"prompt_unchanged_threshold\": 5,\n    \"content_view_radius_lines\": 200,\n    \"content_edit_radius_lines\": 5\n  }\n}"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "CASCADE_USE_SUBAGENT_CHECKPOINTER": {"name": "CASCADE_USE_SUBAGENT_CHECKPOINTER", "type": "release", "description": "", "enabled": false, "strategies": [], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "CASCADE_VIEW_FILE_TOOL_CONFIG_OVERRIDE": {"name": "CASCADE_VIEW_FILE_TOOL_CONFIG_OVERRIDE", "type": "experiment", "description": "Whether to allow Cascade URL web document page reading", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "cascadeModelAlias", "operator": "IN", "values": ["MODEL_ALIAS_CASCADE_BASE", "MODEL_ALIAS_SWE_1", "MODEL_ALIAS_SWE_1_LITE", "MODEL_ALIAS_SHAMU"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "CASCADE_VIEW_FILE_TOOL_CONFIG_OVERRIDE", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "default", "payload": {"type": "json", "value": "{}"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "requestedModelId", "operator": "IN", "values": ["MODEL_UNSPECIFIED"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "CASCADE_VIEW_FILE_TOOL_CONFIG_OVERRIDE", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "default", "payload": {"type": "json", "value": "{}"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-insiders"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "CASCADE_VIEW_FILE_TOOL_CONFIG_OVERRIDE", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "outline", "payload": {"type": "json", "value": "{\n  \"use_prompt_prefix\": true,\n  \"split_outline_tool\": true\n}"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-next"], "value": "", "caseInsensitive": false, "inverted": false}, {"contextName": "ideVersion", "operator": "SEMVER_LT", "values": [], "value": "1.6.110", "caseInsensitive": false, "inverted": true}], "parameters": {"groupId": "cascade-research-03-26", "rollout": "50", "stickiness": "default"}, "segments": null, "variants": [{"name": "Next", "payload": {"type": "json", "value": "{\n  \"use_prompt_prefix\": true,\n  \"split_outline_tool\": true\n}\n"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf"], "value": "", "caseInsensitive": false, "inverted": false}, {"contextName": "ideVersion", "operator": "SEMVER_LT", "values": [], "value": "1.6.0", "caseInsensitive": false, "inverted": true}], "parameters": {"groupId": "cascade-research-03-26", "rollout": "50", "stickiness": "default"}, "segments": null, "variants": [{"name": "outline", "payload": {"type": "json", "value": "{\n  \"use_prompt_prefix\": true,\n  \"split_outline_tool\": true\n}\n"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ideVersion", "operator": "SEMVER_LT", "values": [], "value": "1.3.1", "caseInsensitive": false, "inverted": true}, {"contextName": "ide", "operator": "IN", "values": ["windsurf", "windsurf-next"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "CASCADE_VIEW_FILE_TOOL_CONFIG_OVERRIDE", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "outline_with_prompt", "payload": {"type": "json", "value": "{\n  \"allow_doc_outline\": true,\n  \"use_line_numbers_for_raw\": true,\n  \"use_prompt_prefix\": true\n}"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}, {"name": "raw_with_prompt", "payload": {"type": "json", "value": "{\n  \"allow_doc_outline\": false,\n  \"use_line_numbers_for_raw\": true,\n  \"use_prompt_prefix\": true\n}"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "default", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "CASCADE_WEB_APP_DEPLOYMENTS_ENABLED": {"name": "CASCADE_WEB_APP_DEPLOYMENTS_ENABLED", "type": "experiment", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "devMode", "operator": "IN", "values": ["true"], "value": "", "caseInsensitive": false, "inverted": false}, {"contextName": "ide", "operator": "IN", "values": ["jetbrains"], "value": "", "caseInsensitive": false, "inverted": true}], "parameters": {"groupId": "CASCADE_WEB_APP_DEPLOYMENTS_ENABLED", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-insiders", "windsurf-next"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "CASCADE_WEB_APP_DEPLOYMENTS_ENABLED", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf"], "value": "", "caseInsensitive": false, "inverted": false}, {"contextName": "ideVersion", "operator": "SEMVER_GT", "values": [], "value": "1.6.0", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "CASCADE_WEB_APP_DEPLOYMENTS_ENABLED", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "CASCADE_WEB_SEARCH_ENABLED": {"name": "CASCADE_WEB_SEARCH_ENABLED", "type": "experiment", "description": "Whether to search the web for pages relevant to the user's query.", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "CASCADE_WEB_SEARCH_ENABLED", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "CASCADE_WEB_SEARCH_NUX": {"name": "CASCADE_WEB_SEARCH_NUX", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "CASCADE_WEB_SEARCH_NUX", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "CASCADE_WEB_TOOLS_READ_URLS": {"name": "CASCADE_WEB_TOOLS_READ_URLS", "type": "experiment", "description": "Whether to allow Cascade URL web document page reading", "enabled": false, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-insiders"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "CASCADE_WEB_TOOLS_READ_URLS", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "teamsMode", "operator": "IN", "values": ["false"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "CASCADE_WEB_TOOLS_READ_URLS", "rollout": "25", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "CASCADE_WINDSURF_BROWSER_TOOLS_ENABLED": {"name": "CASCADE_WINDSURF_BROWSER_TOOLS_ENABLED", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-insiders"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "CASCADE_WINDSURF_BROWSER_TOOLS_ENABLED", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf", "windsurf-next"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "CASCADE_WINDSURF_BROWSER_TOOLS_ENABLED", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "CHAT_CLIENT_UNLEASH_TEST": {"name": "CHAT_CLIENT_UNLEASH_TEST", "type": "release", "description": "A flag for testing the Unleash setup in the chat client.", "enabled": false, "strategies": [], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "CHAT_COMPLETION_TOKENS_SOFT_LIMIT": {"name": "CHAT_COMPLETION_TOKENS_SOFT_LIMIT", "type": "release", "description": "Prompt token soft limit for chat completions (command) prompt construction", "enabled": false, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "CHAT_COMPLETION_TOKENS_SOFT_LIMIT", "rollout": "20", "stickiness": "default"}, "segments": null, "variants": [{"name": "SOFT_LIMIT_12000", "payload": {"type": "string", "value": "12000"}, "enabled": false, "featureEnabled": false, "weight": 500, "weightType": "", "stickiness": "default", "overrides": null}, {"name": "SOFT_LIMIT_8000", "payload": {"type": "string", "value": "8000"}, "enabled": false, "featureEnabled": false, "weight": 500, "weightType": "", "stickiness": "default", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "CHAT_MODEL_CONFIG": {"name": "CHAT_MODEL_CONFIG", "type": "operational", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "requestedModelId", "operator": "NOT_IN", "values": ["MODEL_CHAT_GPT_4", "MODEL_CHAT_GPT_4_1106_PREVIEW"], "value": "", "caseInsensitive": false, "inverted": false}, {"contextName": "extensionVersion", "operator": "SEMVER_LT", "values": [], "value": "1.10.8", "caseInsensitive": false, "inverted": true}, {"contextName": "teamsMode", "operator": "IN", "values": ["true"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "CHAT_MODEL_CONFIG", "rollout": "100", "stickiness": "random"}, "segments": null, "variants": [{"name": "MODEL_CHAT_11121", "payload": {"type": "json", "value": "{\n\"model_name\":\"MODEL_CHAT_11121\",\n\"context_check_model_name\":\"MODEL_CHAT_12437\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "random", "overrides": null}, {"name": "MODEL_LLAMA_3_1_70B_INSTRUCT", "payload": {"type": "json", "value": "{\n \"model_name\":\"MODEL_LLAMA_3_1_70B_INSTRUCT\", \"context_check_model_name\":\"MODEL_CHAT_12437\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "random", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "requestedModelId", "operator": "NOT_IN", "values": ["MODEL_CHAT_GPT_4", "MODEL_CHAT_GPT_4_1106_PREVIEW"], "value": "", "caseInsensitive": false, "inverted": false}, {"contextName": "extensionVersion", "operator": "SEMVER_LT", "values": [], "value": "1.10.8", "caseInsensitive": false, "inverted": true}], "parameters": {"groupId": "CHAT_MODEL_CONFIG", "rollout": "100", "stickiness": "random"}, "segments": null, "variants": [{"name": "MODEL_CHAT_11121", "payload": {"type": "json", "value": "{\n\"model_name\":\"MODEL_CHAT_11121\",\n\"context_check_model_name\":\"MODEL_CHAT_12437\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "random", "overrides": null}, {"name": "MODEL_CHAT_12437", "payload": {"type": "json", "value": "{ \n\"model_name\":\"MODEL_CHAT_12437\",\n\"context_check_model_name\":\"MODEL_CHAT_12437\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "random", "overrides": null}, {"name": "MODEL_CHAT_12491", "payload": {"type": "json", "value": "{ \n\"model_name\":\"MODEL_CHAT_12491\", \"context_check_model_name\":\"MODEL_CHAT_12437\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "random", "overrides": null}, {"name": "MODEL_LLAMA_3_1_405B_INSTRUCT", "payload": {"type": "json", "value": "{\n \"model_name\":\"MODEL_LLAMA_3_1_405B_INSTRUCT\", \"context_check_model_name\":\"MODEL_CHAT_12437\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "random", "overrides": null}, {"name": "MODEL_LLAMA_3_1_70B_INSTRUCT", "payload": {"type": "json", "value": "{\n \"model_name\":\"MODEL_LLAMA_3_1_70B_INSTRUCT\", \"context_check_model_name\":\"MODEL_CHAT_12437\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "random", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "requestedModelId", "operator": "IN", "values": ["MODEL_CHAT_GPT_4", "MODEL_CHAT_GPT_4_1106_PREVIEW"], "value": "", "caseInsensitive": false, "inverted": false}, {"contextName": "extensionVersion", "operator": "SEMVER_LT", "values": [], "value": "1.8.51", "caseInsensitive": false, "inverted": true}, {"contextName": "teamsMode", "operator": "IN", "values": ["true"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "CHAT_MODEL_CONFIG", "rollout": "100", "stickiness": "random"}, "segments": null, "variants": [{"name": "MODEL_CHAT_11121", "payload": {"type": "json", "value": "{\n\"model_name\":\"MODEL_CHAT_11121\",\n\"context_check_model_name\":\"MODEL_CHAT_12437\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "random", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "requestedModelId", "operator": "NOT_IN", "values": ["MODEL_CHAT_GPT_4", "MODEL_CHAT_GPT_4_1106_PREVIEW"], "value": "", "caseInsensitive": false, "inverted": false}, {"contextName": "extensionVersion", "operator": "SEMVER_LT", "values": [], "value": "1.8.51", "caseInsensitive": false, "inverted": true}], "parameters": {"groupId": "CHAT_MODEL_CONFIG", "rollout": "100", "stickiness": "random"}, "segments": null, "variants": [{"name": "MODEL_CHAT_11121", "payload": {"type": "json", "value": "{\n\"model_name\":\"MODEL_CHAT_11121\",\n\"context_check_model_name\":\"MODEL_CHAT_12437\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 950, "weightType": "", "stickiness": "random", "overrides": null}, {"name": "MODEL_CHAT_12437", "payload": {"type": "json", "value": "{ \n\"model_name\":\"MODEL_CHAT_12437\",\n\"context_check_model_name\":\"MODEL_CHAT_12437\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "random", "overrides": null}, {"name": "MODEL_CHAT_12491", "payload": {"type": "json", "value": "{ \n\"model_name\":\"MODEL_CHAT_12491\", \"context_check_model_name\":\"MODEL_CHAT_12437\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 50, "weightType": "", "stickiness": "random", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "requestedModelId", "operator": "NOT_IN", "values": ["MODEL_CHAT_GPT_4", "MODEL_CHAT_GPT_4_1106_PREVIEW"], "value": "", "caseInsensitive": false, "inverted": false}, {"contextName": "extensionVersion", "operator": "SEMVER_LT", "values": [], "value": "1.8.15", "caseInsensitive": false, "inverted": true}], "parameters": {"groupId": "CHAT_MODEL_CONFIG", "rollout": "100", "stickiness": "random"}, "segments": null, "variants": [{"name": "MODEL_CHAT_11121", "payload": {"type": "json", "value": "{\n\"model_name\":\"MODEL_CHAT_11121\",\n\"context_check_model_name\":\"MODEL_CHAT_12437\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "random", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "requestedModelId", "operator": "NOT_IN", "values": ["MODEL_CHAT_GPT_4", "MODEL_CHAT_GPT_4_1106_PREVIEW"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "CHAT_MODEL_CONFIG", "rollout": "100", "stickiness": "random"}, "segments": null, "variants": [{"name": "chat35_turbo", "payload": {"type": "json", "value": "{\n\"model_name\":\"MODEL_CHAT_3_5_TURBO\", \"context_check_model_name\":\"MODEL_CHAT_12437\" \n}"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "random", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "CHAT_MODEL_CONFIG", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "MODEL_CHAT_11121", "payload": {"type": "json", "value": "{\n  \"model_name\":\"MODEL_CHAT_11121\", \n  \"context_check_model_name\":\"MODEL_CHAT_11121\" \n}"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [{"name": "chat35_turbo", "payload": {"type": "json", "value": "{\n\"model_name\":\"MODEL_CHAT_3_5_TURBO\", \"context_check_model_name\":\"MODEL_CHAT_12437\" \n}"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "variable", "stickiness": "random", "overrides": [{"contextName": "extensionVersion", "values": ["1.8.21", "1.8.20", "1.8.19", "1.8.18", "1.8.17", "1.8.16", "1.8.15", "1.8.14"]}]}, {"name": "gpt_4", "payload": {"type": "json", "value": "{\n\"model_name\":\"MODEL_CHAT_GPT_4_1106_PREVIEW\",\n\"context_check_model_name\":\"MODEL_CHAT_12437\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "fix", "stickiness": "random", "overrides": []}, {"name": "gpt_4o", "payload": {"type": "json", "value": "{\n\"model_name\":\"MODEL_CHAT_GPT_4O_2024_05_13\",\n\"context_check_model_name\":\"MODEL_CHAT_12437\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "fix", "stickiness": "random", "overrides": []}, {"name": "gpt35turbo_1106", "payload": {"type": "json", "value": "{\n\"model_name\":\"MODEL_CHAT_GPT_3_5_TURBO_1106\",\n\"context_check_model_name\":\"MODEL_CHAT_12437\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "fix", "stickiness": "random", "overrides": []}, {"name": "MODEL_CHAT_11121", "payload": {"type": "json", "value": "{\n\"model_name\":\"MODEL_CHAT_11121\",\n\"context_check_model_name\":\"MODEL_CHAT_12437\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "fix", "stickiness": "random", "overrides": []}, {"name": "MODEL_CHAT_12437", "payload": {"type": "json", "value": "{ \n\"model_name\":\"MODEL_CHAT_12437\",\n\"context_check_model_name\":\"MODEL_CHAT_12437\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "fix", "stickiness": "random", "overrides": []}, {"name": "MODEL_CHAT_12491", "payload": {"type": "json", "value": "{ \n\"model_name\":\"MODEL_CHAT_12491\", \"context_check_model_name\":\"MODEL_CHAT_12437\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "fix", "stickiness": "random", "overrides": []}, {"name": "MODEL_CHAT_12968", "payload": {"type": "json", "value": "{\n\"model_name\":\"MODEL_CHAT_12968\", \"context_check_model_name\":\"MODEL_CHAT_12437\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "fix", "stickiness": "random", "overrides": []}, {"name": "MODEL_LLAMA_3_1_405B_INSTRUCT", "payload": {"type": "json", "value": "{\n \"model_name\":\"MODEL_LLAMA_3_1_405B_INSTRUCT\", \"context_check_model_name\":\"MODEL_CHAT_12437\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "fix", "stickiness": "random", "overrides": []}, {"name": "MODEL_LLAMA_3_1_70B_INSTRUCT", "payload": {"type": "json", "value": "{\n \"model_name\":\"MODEL_LLAMA_3_1_70B_INSTRUCT\", \"context_check_model_name\":\"MODEL_CHAT_12437\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "fix", "stickiness": "random", "overrides": [{"contextName": "teamsMode", "values": ["true"]}]}], "dependencies": null, "impressionData": true}, "CHAT_TOKENS_SOFT_LIMIT": {"name": "CHAT_TOKENS_SOFT_LIMIT", "type": "release", "description": "Soft token limit to use for chat prompt construction", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "CHAT_TOKENS_SOFT_LIMIT", "rollout": "5", "stickiness": "userId"}, "segments": null, "variants": [{"name": "8000", "payload": {"type": "string", "value": "8000"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "userId", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "CHAT_TOKENS_SOFT_LIMIT", "rollout": "100", "stickiness": "userId"}, "segments": null, "variants": [{"name": "6000", "payload": {"type": "string", "value": "6000"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "userId", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": true}, "CM_MEMORY_TELEMETRY": {"name": "CM_MEMORY_TELEMETRY", "type": "experiment", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "CM_MEMORY_TELEMETRY", "rollout": "10", "stickiness": "random"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "COLLAPSE_ASSISTANT_MESSAGES": {"name": "COLLAPSE_ASSISTANT_MESSAGES", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ideVersion", "operator": "SEMVER_GT", "values": [], "value": "1.5.9", "caseInsensitive": false, "inverted": false}, {"contextName": "requestedModelId", "operator": "IN", "values": ["MODEL_CHAT_O3_MINI", "MODEL_GOOGLE_GEMINI_2_5_PRO", "MODEL_XAI_GROK_3", "MODEL_XAI_GROK_3_MINI_REASONING"], "value": "", "caseInsensitive": false, "inverted": false}, {"contextName": "ideVersion", "operator": "SEMVER_LT", "values": [], "value": "1.8.0", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "COLLAPSE_ASSISTANT_MESSAGES", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ideVersion", "operator": "SEMVER_GT", "values": [], "value": "1.5.9", "caseInsensitive": false, "inverted": false}, {"contextName": "requestedModelId", "operator": "IN", "values": ["MODEL_CASCADE_20065", "MODEL_CASCADE_20066", "MODEL_CASCADE_20068", "MODEL_CASCADE_20069"], "value": "", "caseInsensitive": false, "inverted": false}, {"contextName": "ideVersion", "operator": "SEMVER_LT", "values": [], "value": "1.8.0", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "COLLAPSE_ASSISTANT_MESSAGES", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "COLLECT_ONBOARDING_EVENTS": {"name": "COLLECT_ONBOARDING_EVENTS", "type": "experiment", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "COLLECT_ONBOARDING_EVENTS", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "COMBINED_MODEL_USE_FULL_INSTRUCTION_FOR_RETRIEVAL": {"name": "COMBINED_MODEL_USE_FULL_INSTRUCTION_FOR_RETRIEVAL", "type": "experiment", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "COMBINED_MODEL_USE_FULL_INSTRUCTION_FOR_RETRIEVAL", "rollout": "100", "stickiness": "random"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": true}, "COMMAND_BOX_ON_TOP": {"name": "COMMAND_BOX_ON_TOP", "type": "experiment", "description": "A flag to determine the command palette vs comment box", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "COMMAND_BOX_ON_TOP", "rollout": "100", "stickiness": "userId"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "COMMAND_INJECT_USER_MEMORIES": {"name": "COMMAND_INJECT_USER_MEMORIES", "type": "experiment", "description": "", "enabled": false, "strategies": [], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": true}, "COMMAND_MODEL_CONFIG": {"name": "COMMAND_MODEL_CONFIG", "type": "experiment", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "teamsMode", "operator": "IN", "values": ["true"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "COMMAND_MODEL_CONFIG", "rollout": "100", "stickiness": "random"}, "segments": null, "variants": [{"name": "MODEL_CHAT_11121", "payload": {"type": "json", "value": "{\n\"model_name\":\"MODEL_CHAT_11121\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "random", "overrides": null}, {"name": "MODEL_CHAT_12119", "payload": {"type": "json", "value": "{\n\"model_name\":\"MODEL_CHAT_12119\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "random", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "extensionVersion", "operator": "SEMVER_LT", "values": [], "value": "1.22.4", "caseInsensitive": false, "inverted": true}], "parameters": {"groupId": "COMMAND_MODEL_CONFIG", "rollout": "100", "stickiness": "random"}, "segments": null, "variants": [{"name": "MODEL_CHAT_12119", "payload": {"type": "json", "value": "{\n\"model_name\":\"MODEL_CHAT_12119\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 750, "weightType": "", "stickiness": "random", "overrides": null}, {"name": "MODEL_GPT4o", "payload": {"type": "json", "value": "{\n  \"model_name\": \"MODEL_CHAT_GPT_4O_2024_08_06\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 250, "weightType": "", "stickiness": "random", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "extensionVersion", "operator": "SEMVER_LT", "values": [], "value": "1.14.6", "caseInsensitive": false, "inverted": true}], "parameters": {"groupId": "COMMAND_MODEL_CONFIG", "rollout": "100", "stickiness": "random"}, "segments": null, "variants": [{"name": "MODEL_CHAT_11121", "payload": {"type": "json", "value": "{\n\"model_name\":\"MODEL_CHAT_11121\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "random", "overrides": null}, {"name": "MODEL_CHAT_12119", "payload": {"type": "json", "value": "{\n\"model_name\":\"MODEL_CHAT_12119\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "random", "overrides": null}, {"name": "MODEL_CHAT_13566", "payload": {"type": "json", "value": "{\n\"model_name\":\"MODEL_CHAT_13566\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "random", "overrides": null}, {"name": "MODEL_CHAT_14255", "payload": {"type": "json", "value": "{\n\"model_name\":\"MODEL_CHAT_14255\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "random", "overrides": null}, {"name": "MODEL_CHAT_14256", "payload": {"type": "json", "value": "{\n\"model_name\":\"MODEL_CHAT_14256\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "random", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "COMMAND_MODEL_CONFIG", "rollout": "100", "stickiness": "random"}, "segments": null, "variants": [{"name": "MODEL_CHAT_11121", "payload": {"type": "json", "value": "{\n\"model_name\":\"MODEL_CHAT_11121\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "random", "overrides": null}, {"name": "MODEL_CHAT_12119", "payload": {"type": "json", "value": "{\n\"model_name\":\"MODEL_CHAT_12119\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "random", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "COMMAND_PROMPT_CACHE_CONFIG": {"name": "COMMAND_PROMPT_CACHE_CONFIG", "type": "experiment", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "COMMAND_PROMPT_CACHE_CONFIG", "rollout": "100", "stickiness": "sessionId"}, "segments": null, "variants": [{"name": "10files_10minutes", "payload": {"type": "json", "value": "{\n  \"max_tracked_files\":10,\n  \"max_cache_age_seconds\": 600\n}"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "sessionId", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": true}, "COMMIT_GRAPH": {"name": "COMMIT_GRAPH", "type": "experiment", "description": "Enables the commit graph as a provider in the context module, and the related files tool in cascade", "enabled": false, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "extensionVersion", "operator": "SEMVER_GT", "values": [], "value": "1.24.0", "caseInsensitive": false, "inverted": false}, {"contextName": "prereleaseMode", "operator": "IN", "values": ["true"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "COMMIT_GRAPH", "rollout": "5", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": true}, "COMPLETIONS_CCI_REFRESH_TIMEOUT_MS": {"name": "COMPLETIONS_CCI_REFRESH_TIMEOUT_MS", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "COMPLETIONS_CCI_REFRESH_TIMEOUT_MS", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "low_allowance", "payload": {"type": "number", "value": "50"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "COMPLETIONS_MAX_VALID_ACTIONS_BEFORE_DISMISS": {"name": "COMPLETIONS_MAX_VALID_ACTIONS_BEFORE_DISMISS", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "COMPLETIONS_MAX_VALID_ACTIONS_BEFORE_DISMISS", "rollout": "100", "stickiness": "userId"}, "segments": null, "variants": [{"name": "COMPLETIONS_MAX_VALID_ACTIONS_BEFORE_DISMISS", "payload": {"type": "number", "value": "10"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "userId", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "COMPLETIONS_USE_COMBINED_MODEL": {"name": "COMPLETIONS_USE_COMBINED_MODEL", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-insiders"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "COMPLETIONS_USE_COMBINED_MODEL", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-next"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "COMPLETIONS_USE_COMBINED_MODEL", "rollout": "100", "stickiness": "random"}, "segments": null, "variants": []}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf"], "value": "", "caseInsensitive": false, "inverted": false}, {"contextName": "ideVersion", "operator": "SEMVER_LT", "values": [], "value": "1.2.4", "caseInsensitive": false, "inverted": true}], "parameters": {"groupId": "COMPLETIONS_USE_COMBINED_MODEL", "rollout": "100", "stickiness": "random"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "COMPLETION_SPEED_BLOCK_NAVIGATION_TAB_JUMP_ON_PREDICTIVE_SUPERCOMPLETE": {"name": "COMPLETION_SPEED_BLOCK_NAVIGATION_TAB_JUMP_ON_PREDICTIVE_SUPERCOMPLETE", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-insiders", "windsurf-next"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "COMPLETION_SPEED_BLOCK_NAVIGATION_TAB_JUMP_ON_PREDICTIVE_SUPERCOMPLETE", "rollout": "100", "stickiness": "userId"}, "segments": null, "variants": []}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "COMPLETION_SPEED_BLOCK_NAVIGATION_TAB_JUMP_ON_PREDICTIVE_SUPERCOMPLETE", "rollout": "100", "stickiness": "userId"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "COMPLETION_SPEED_BLOCK_TAB_JUMP_ON_PREDICTIVE_SUPERCOMPLETE": {"name": "COMPLETION_SPEED_BLOCK_TAB_JUMP_ON_PREDICTIVE_SUPERCOMPLETE", "type": "release", "description": "", "enabled": false, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-insiders", "windsurf-next"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "COMPLETION_SPEED_BLOCK_TAB_JUMP_ON_PREDICTIVE_SUPERCOMPLETE", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "COMPLETION_SPEED_BLOCK_TYPING_TAB_JUMP_ON_PREDICTIVE_SUPERCOMPLETE": {"name": "COMPLETION_SPEED_BLOCK_TYPING_TAB_JUMP_ON_PREDICTIVE_SUPERCOMPLETE", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-insiders", "windsurf-next"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "COMPLETION_SPEED_BLOCK_TYPING_TAB_JUMP_ON_PREDICTIVE_SUPERCOMPLETE", "rollout": "100", "stickiness": "userId"}, "segments": null, "variants": []}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "COMPLETION_SPEED_BLOCK_TYPING_TAB_JUMP_ON_PREDICTIVE_SUPERCOMPLETE", "rollout": "100", "stickiness": "userId"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "COMPLETION_SPEED_PREDICTIVE_SUPERCOMPLETE": {"name": "COMPLETION_SPEED_PREDICTIVE_SUPERCOMPLETE", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "COMPLETION_SPEED_PREDICTIVE_SUPERCOMPLETE", "rollout": "100", "stickiness": "userId"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "COMPLETION_SPEED_PREDICTIVE_TAB_JUMP": {"name": "COMPLETION_SPEED_PREDICTIVE_TAB_JUMP", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "COMPLETION_SPEED_PREDICTIVE_TAB_JUMP", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "COMPLETION_SPEED_SUPERCOMPLETE_CACHE": {"name": "COMPLETION_SPEED_SUPERCOMPLETE_CACHE", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-insiders"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "COMPLETION_SPEED_SUPERCOMPLETE_CACHE", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-next"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "COMPLETION_SPEED_SUPERCOMPLETE_CACHE", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf"], "value": "", "caseInsensitive": false, "inverted": false}, {"contextName": "extensionVersion", "operator": "SEMVER_GT", "values": [], "value": "1.32.1", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "COMPLETION_SPEED_SUPERCOMPLETE_CACHE", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "COMPLETION_SPEED_TAB_JUMP_CACHE": {"name": "COMPLETION_SPEED_TAB_JUMP_CACHE", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-insiders"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "COMPLETION_SPEED_TAB_JUMP_CACHE", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-next"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "COMPLETION_SPEED_TAB_JUMP_CACHE", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf"], "value": "", "caseInsensitive": false, "inverted": false}, {"contextName": "extensionVersion", "operator": "SEMVER_GT", "values": [], "value": "1.32.1", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "COMPLETION_SPEED_TAB_JUMP_CACHE", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}, {"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "COMPLETION_SPEED_TAB_JUMP_CACHE", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "CONTEXT_ACTIVE_DOCUMENT_FRACTION": {"name": "CONTEXT_ACTIVE_DOCUMENT_FRACTION", "type": "experiment", "description": "Force pinning the active document and open documents to the context for retrieval", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "CONTEXT_ACTIVE_DOCUMENT_FRACTION", "rollout": "66", "stickiness": "random"}, "segments": null, "variants": [{"name": "10", "payload": {"type": "string", "value": "0.1"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "random", "overrides": null}, {"name": "20", "payload": {"type": "string", "value": "0.2"}, "enabled": false, "featureEnabled": false, "weight": 500, "weightType": "", "stickiness": "random", "overrides": null}, {"name": "40", "payload": {"type": "string", "value": "0.4"}, "enabled": false, "featureEnabled": false, "weight": 500, "weightType": "", "stickiness": "random", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "CONTEXT_COMMAND_TRAJECTORY_PROMPT_CONFIG": {"name": "CONTEXT_COMMAND_TRAJECTORY_PROMPT_CONFIG", "type": "release", "description": "", "enabled": false, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "extensionVersion", "operator": "SEMVER_GT", "values": [], "value": "1.20.11", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "CONTEXT_COMMAND_TRAJECTORY_PROMPT_CONFIG", "rollout": "50", "stickiness": "random"}, "segments": null, "variants": [{"name": "0.2", "payload": {"type": "json", "value": "{\n  \"prompt_fraction\":\"0.2\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "random", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "CONTEXT_DOCUMENT_OUTLINE": {"name": "CONTEXT_DOCUMENT_OUTLINE", "type": "experiment", "description": "Force pinning the active document and open documents to the context for retrieval", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "extensionVersion", "operator": "SEMVER_GT", "values": [], "value": "1.21.11", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "CONTEXT_DOCUMENT_OUTLINE", "rollout": "50", "stickiness": "random"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "CONTEXT_FOR_AUTOCOMPLETE": {"name": "CONTEXT_FOR_AUTOCOMPLETE", "type": "experiment", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "extensionVersion", "operator": "SEMVER_LT", "values": [], "value": "1.2.53", "caseInsensitive": false, "inverted": true}], "parameters": {"groupId": "CONTEXT", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "CONTEXT_FOR_CHAT": {"name": "CONTEXT_FOR_CHAT", "type": "experiment", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "CONTEXT", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "CONTEXT_FOR_NONGENERIC_CHAT": {"name": "CONTEXT_FOR_NONGENERIC_CHAT", "type": "experiment", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "CONTEXT_FOR_NONGENERIC_CHAT", "rollout": "50", "stickiness": "random"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": true}, "CORTEX_CONFIG": {"name": "CORTEX_CONFIG", "type": "release", "description": "Settings for cortex", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "extensionVersion", "operator": "SEMVER_GT", "values": [], "value": "1.8.46", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "CORTEX_CONFIG", "rollout": "100", "stickiness": "userId"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [{"name": "gpt4o", "payload": {"type": "json", "value": "{\n\t\"code_plan_config\": {\n\t\t\"model_config\": {\n\t\t\t\"model\": 71,\n\t\t\t\"temperature\": 0.15,\n\t\t\t\"max_input_tokens\": 24000,\n\t\t\t\"max_output_tokens\": 2000\n\t\t}\n\t}\n}"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "variable", "stickiness": "default", "overrides": []}], "dependencies": null, "impressionData": false}, "COUNTRY_KILL_SWITCH": {"name": "COUNTRY_KILL_SWITCH", "type": "kill-switch", "description": "", "enabled": false, "strategies": [], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "CROSS_SELL_EXTENSION_DOWNLOAD_WINDSURF": {"name": "CROSS_SELL_EXTENSION_DOWNLOAD_WINDSURF", "type": "release", "description": "Show download windsurf from the extension", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "extensionVersion", "operator": "IN", "values": ["1.31.18"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "CROSS_SELL_EXTENSION_DOWNLOAD_WINDSURF", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "country", "operator": "IN", "values": ["China", "India", "china", "india", "CH", "IN"], "value": "", "caseInsensitive": false, "inverted": true}], "parameters": {"groupId": "CROSS_SELL_EXTENSION_DOWNLOAD_WINDSURF", "rollout": "10", "stickiness": "default"}, "segments": null, "variants": []}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "devMode", "operator": "IN", "values": ["true"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "CROSS_SELL_EXTENSION_DOWNLOAD_WINDSURF", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "country", "operator": "IN", "values": ["us", "US", "United States", "usa", "USA"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "CROSS_SELL_EXTENSION_DOWNLOAD_WINDSURF", "rollout": "15", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "CUMULATIVE_PROMPT_CONFIG": {"name": "CUMULATIVE_PROMPT_CONFIG", "type": "experiment", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-insiders"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "CUMULATIVE_PROMPT_CONFIG", "rollout": "100", "stickiness": "sessionId"}, "segments": null, "variants": [{"name": "default_16k", "payload": {"type": "json", "value": "{\n    \"persistent_context_multiplier\": 0.33,\n    \"persistent_active_document_multiplier\": 0.5,\n    \"persistent_open_docs_multiplier\": 0.25,\n    \"persistent_max_tokens_per_open_doc\": 2048,\n    \"persistent_max_ccis_considered\": 25,\n    \"trajectory_context_multiplier\": 0.6,\n    \"trajectory_refresh_threshold_multiplier\": 0.9,\n    \"trajectory_truncation_multiplier\": 0.5,\n    \"ephemeral_context_multiplier\": 0.07,\n    \"intent_reservation_tokens\": 512,\n    \"ephemeral_active_document_multiplier\": 0.5,\n    \"ephemeral_max_ccis_considered\": 25\n}"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "sessionId", "overrides": null}, {"name": "default_16k_no_ccis", "payload": {"type": "json", "value": "{\n    \"persistent_context_multiplier\": 0.33,\n    \"persistent_active_document_multiplier\": 0.5,\n    \"persistent_open_docs_multiplier\": 0.5,\n    \"persistent_max_tokens_per_open_doc\": 2048,\n    \"persistent_max_ccis_considered\": 0,\n    \"trajectory_context_multiplier\": 0.6,\n    \"trajectory_refresh_threshold_multiplier\": 0.9,\n    \"trajectory_truncation_multiplier\": 0.5,\n    \"ephemeral_context_multiplier\": 0.07,\n    \"intent_reservation_tokens\": 512,\n    \"ephemeral_active_document_multiplier\": 0.9,\n    \"ephemeral_max_ccis_considered\": 0\n}"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "sessionId", "overrides": null}, {"name": "default_8k", "payload": {"type": "json", "value": "{\n    \"persistent_context_multiplier\": 0.35,\n    \"persistent_active_document_multiplier\": 0.5,\n    \"persistent_open_docs_multiplier\": 0.25,\n    \"persistent_max_tokens_per_open_doc\": 2048,\n    \"persistent_max_ccis_considered\": 25,\n    \"trajectory_context_multiplier\": 0.525,\n    \"trajectory_refresh_threshold_multiplier\": 0.9,\n    \"trajectory_truncation_multiplier\": 0.5,\n    \"ephemeral_context_multiplier\": 0.125,\n    \"intent_reservation_tokens\": 256,\n    \"ephemeral_active_document_multiplier\": 0.5,\n    \"ephemeral_max_ccis_considered\": 25\n}"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "sessionId", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf"], "value": "", "caseInsensitive": false, "inverted": false}, {"contextName": "planName", "operator": "IN", "values": ["Free"], "value": "", "caseInsensitive": false, "inverted": true}], "parameters": {"groupId": "CUMULATIVE_PROMPT_CONFIG", "rollout": "100", "stickiness": "sessionId"}, "segments": null, "variants": [{"name": "default_16k", "payload": {"type": "json", "value": "{\n    \"persistent_context_multiplier\": 0.33,\n    \"persistent_active_document_multiplier\": 0.5,\n    \"persistent_open_docs_multiplier\": 0.25,\n    \"persistent_max_tokens_per_open_doc\": 2048,\n    \"persistent_max_ccis_considered\": 25,\n    \"trajectory_context_multiplier\": 0.6,\n    \"trajectory_refresh_threshold_multiplier\": 0.9,\n    \"trajectory_truncation_multiplier\": 0.5,\n    \"ephemeral_context_multiplier\": 0.07,\n    \"intent_reservation_tokens\": 512,\n    \"ephemeral_active_document_multiplier\": 0.5,\n    \"ephemeral_max_ccis_considered\": 25\n}"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "sessionId", "overrides": null}, {"name": "default_8k", "payload": {"type": "json", "value": "{\n    \"persistent_context_multiplier\": 0.35,\n    \"persistent_active_document_multiplier\": 0.5,\n    \"persistent_open_docs_multiplier\": 0.25,\n    \"persistent_max_tokens_per_open_doc\": 2048,\n    \"persistent_max_ccis_considered\": 25,\n    \"trajectory_context_multiplier\": 0.525,\n    \"trajectory_refresh_threshold_multiplier\": 0.9,\n    \"trajectory_truncation_multiplier\": 0.5,\n    \"ephemeral_context_multiplier\": 0.125,\n    \"intent_reservation_tokens\": 256,\n    \"ephemeral_active_document_multiplier\": 0.5,\n    \"ephemeral_max_ccis_considered\": 25\n}"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "sessionId", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-next"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "CUMULATIVE_PROMPT_CONFIG", "rollout": "100", "stickiness": "sessionId"}, "segments": null, "variants": [{"name": "default_16k", "payload": {"type": "json", "value": "{\n    \"persistent_context_multiplier\": 0.33,\n    \"persistent_active_document_multiplier\": 0.5,\n    \"persistent_open_docs_multiplier\": 0.25,\n    \"persistent_max_tokens_per_open_doc\": 2048,\n    \"persistent_max_ccis_considered\": 25,\n    \"trajectory_context_multiplier\": 0.6,\n    \"trajectory_refresh_threshold_multiplier\": 0.9,\n    \"trajectory_truncation_multiplier\": 0.5,\n    \"ephemeral_context_multiplier\": 0.07,\n    \"intent_reservation_tokens\": 512,\n    \"ephemeral_active_document_multiplier\": 0.5,\n    \"ephemeral_max_ccis_considered\": 25\n}"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "sessionId", "overrides": null}, {"name": "default_16k_no_ccis", "payload": {"type": "json", "value": "{\n    \"persistent_context_multiplier\": 0.33,\n    \"persistent_active_document_multiplier\": 0.5,\n    \"persistent_open_docs_multiplier\": 0.5,\n    \"persistent_max_tokens_per_open_doc\": 2048,\n    \"persistent_max_ccis_considered\": 0,\n    \"trajectory_context_multiplier\": 0.6,\n    \"trajectory_refresh_threshold_multiplier\": 0.9,\n    \"trajectory_truncation_multiplier\": 0.5,\n    \"ephemeral_context_multiplier\": 0.07,\n    \"intent_reservation_tokens\": 512,\n    \"ephemeral_active_document_multiplier\": 0.9,\n    \"ephemeral_max_ccis_considered\": 0\n}"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "sessionId", "overrides": null}, {"name": "default_8k", "payload": {"type": "json", "value": "{\n    \"persistent_context_multiplier\": 0.35,\n    \"persistent_active_document_multiplier\": 0.5,\n    \"persistent_open_docs_multiplier\": 0.25,\n    \"persistent_max_tokens_per_open_doc\": 2048,\n    \"persistent_max_ccis_considered\": 25,\n    \"trajectory_context_multiplier\": 0.525,\n    \"trajectory_refresh_threshold_multiplier\": 0.9,\n    \"trajectory_truncation_multiplier\": 0.5,\n    \"ephemeral_context_multiplier\": 0.125,\n    \"intent_reservation_tokens\": 256,\n    \"ephemeral_active_document_multiplier\": 0.5,\n    \"ephemeral_max_ccis_considered\": 25\n}"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "sessionId", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "CUMULATIVE_PROMPT_CONFIG", "rollout": "100", "stickiness": "sessionId"}, "segments": null, "variants": [{"name": "default_16k", "payload": {"type": "json", "value": "{\n    \"persistent_context_multiplier\": 0.33,\n    \"persistent_active_document_multiplier\": 0.5,\n    \"persistent_open_docs_multiplier\": 0.25,\n    \"persistent_max_tokens_per_open_doc\": 2048,\n    \"persistent_max_ccis_considered\": 25,\n    \"trajectory_context_multiplier\": 0.6,\n    \"trajectory_refresh_threshold_multiplier\": 0.9,\n    \"trajectory_truncation_multiplier\": 0.5,\n    \"ephemeral_context_multiplier\": 0.07,\n    \"intent_reservation_tokens\": 512,\n    \"ephemeral_active_document_multiplier\": 0.5,\n    \"ephemeral_max_ccis_considered\": 25\n}"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "sessionId", "overrides": null}, {"name": "default_8k", "payload": {"type": "json", "value": "{\n    \"persistent_context_multiplier\": 0.35,\n    \"persistent_active_document_multiplier\": 0.5,\n    \"persistent_open_docs_multiplier\": 0.25,\n    \"persistent_max_tokens_per_open_doc\": 2048,\n    \"persistent_max_ccis_considered\": 25,\n    \"trajectory_context_multiplier\": 0.525,\n    \"trajectory_refresh_threshold_multiplier\": 0.9,\n    \"trajectory_truncation_multiplier\": 0.5,\n    \"ephemeral_context_multiplier\": 0.125,\n    \"intent_reservation_tokens\": 256,\n    \"ephemeral_active_document_multiplier\": 0.5,\n    \"ephemeral_max_ccis_considered\": 25\n}"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "sessionId", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": true}, "CUSTOM_LANGUAGE_IMPORT_REGEX": {"name": "CUSTOM_LANGUAGE_IMPORT_REGEX", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "CUSTOM_LANGUAGE_IMPORT_REGEX", "rollout": "100", "stickiness": "userId"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "DEBUGGING_EXPERIMENT": {"name": "DEBUGGING_EXPERIMENT", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "planName", "operator": "IN", "values": ["Free"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "DEBUGGING_EXPERIMENT", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "DEFAULT_ENABLE_SEARCH": {"name": "DEFAULT_ENABLE_SEARCH", "type": "experiment", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "DEFAULT_ENABLE_SEARCH", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "DISABLE_COMPLETIONS_CACHE": {"name": "DISABLE_COMPLETIONS_CACHE", "type": "release", "description": "", "enabled": false, "strategies": [{"id": 0, "name": "default", "constraints": [], "parameters": {}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "DISABLE_GCP_API_SERVER_FOR_PREMIUM_CHAT": {"name": "DISABLE_GCP_API_SERVER_FOR_PREMIUM_CHAT", "type": "release", "description": "", "enabled": false, "strategies": [], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "DISABLE_IDE_COMPLETIONS_DEBOUNCE": {"name": "DISABLE_IDE_COMPLETIONS_DEBOUNCE", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-insiders", "windsurf-next", "windsurf"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "DISABLE_IDE_COMPLETIONS_DEBOUNCE", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "DISABLE_INFERENCE_API_SERVER": {"name": "DISABLE_INFERENCE_API_SERVER", "type": "release", "description": "", "enabled": false, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "DISABLE_INFERENCE_API_SERVER", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "EFFICIENT_SUPERCOMPLETE_ACCEPT": {"name": "EFFICIENT_SUPERCOMPLETE_ACCEPT", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "EFFICIENT_SUPERCOMPLETE_ACCEPT", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "ENABLE_AUTOCOMPLETE_DURING_INTELLISENSE": {"name": "ENABLE_AUTOCOMPLETE_DURING_INTELLISENSE", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "ENABLE_AUTOCOMPLETE_DURING_INTELLISENSE", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "ENABLE_BACKGROUND_RESEARCH": {"name": "ENABLE_BACKGROUND_RESEARCH", "type": "release", "description": "", "enabled": false, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "extensionVersion", "operator": "SEMVER_LT", "values": [], "value": "1.24.1", "caseInsensitive": false, "inverted": true}, {"contextName": "devMode", "operator": "IN", "values": ["true"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "ENABLE_BACKGROUND_RESEARCH", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "ENABLE_CASCADE_SEGMENT_ANALYTICS": {"name": "ENABLE_CASCADE_SEGMENT_ANALYTICS", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "devMode", "operator": "IN", "values": ["true"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "ENABLE_CASCADE_SEGMENT_ANALYTICS", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}, {"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "ENABLE_CASCADE_SEGMENT_ANALYTICS", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "ENABLE_COMMIT_MESSAGE_GENERATION": {"name": "ENABLE_COMMIT_MESSAGE_GENERATION", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-insiders"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "ENABLE_COMMIT_MESSAGE_GENERATION", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-next"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "ENABLE_COMMIT_MESSAGE_GENERATION", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "ENABLE_COMMIT_MESSAGE_GENERATION", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "ENABLE_CONTENT_FILTER": {"name": "ENABLE_CONTENT_FILTER", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "ENABLE_CONTENT_FILTER", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "ENABLE_QUICK_ACTIONS": {"name": "ENABLE_QUICK_ACTIONS", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-insiders"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "ENABLE_QUICK_ACTIONS", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-next", "windsurf"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "ENABLE_QUICK_ACTIONS", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "ENABLE_RUN_COMMAND": {"name": "ENABLE_RUN_COMMAND", "type": "experiment", "description": "Force pinning the active document and open documents to the context for retrieval", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "extensionVersion", "operator": "SEMVER_GT", "values": [], "value": "1.26.1", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "ENABLE_RUN_COMMAND", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "ENABLE_SHELL_COMMAND_TRAJECTORY": {"name": "ENABLE_SHELL_COMMAND_TRAJECTORY", "type": "experiment", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-insiders"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "ENABLE_SHELL_COMMAND_TRAJECTORY", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-next"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "ENABLE_SHELL_COMMAND_TRAJECTORY", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf"], "value": "", "caseInsensitive": false, "inverted": false}, {"contextName": "ideVersion", "operator": "SEMVER_LT", "values": [], "value": "1.5.6", "caseInsensitive": false, "inverted": true}], "parameters": {"groupId": "ENABLE_SHELL_COMMAND_TRAJECTORY", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "ENABLE_SUGGESTED_RESPONSES": {"name": "ENABLE_SUGGESTED_RESPONSES", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "devMode", "operator": "IN", "values": ["true"], "value": "", "caseInsensitive": false, "inverted": false}, {"contextName": "ide", "operator": "IN", "values": ["jetbrains"], "value": "", "caseInsensitive": false, "inverted": true}], "parameters": {"groupId": "ENABLE_SUGGESTED_RESPONSES", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ideVersion", "operator": "SEMVER_LT", "values": [], "value": "1.4.0", "caseInsensitive": false, "inverted": true}, {"contextName": "requestedModelId", "operator": "IN", "values": ["MODEL_GOOGLE_GEMINI_2_5_PRO"], "value": "", "caseInsensitive": false, "inverted": true}], "parameters": {"groupId": "ENABLE_SUGGESTED_RESPONSES", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "ENABLE_SUPERCOMPLETE": {"name": "ENABLE_SUPERCOMPLETE", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "devMode", "operator": "IN", "values": ["true"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "ENABLE_SUPERCOMPLETE", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-insiders"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "ENABLE_SUPERCOMPLETE", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "ENABLE_SUPERCOMPLETE", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-next"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "ENABLE_SUPERCOMPLETE", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "ESTIMATE_TOKENIZER_KILL_SWITCH": {"name": "ESTIMATE_TOKENIZER_KILL_SWITCH", "type": "experiment", "description": "", "enabled": false, "strategies": [], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": true}, "EXAMPLE_WINDSURF_FEATURE_FLAG": {"name": "EXAMPLE_WINDSURF_FEATURE_FLAG", "type": "release", "description": "An example feature flag for windsurf", "enabled": false, "strategies": [], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "EXTERNAL_MODEL_STREAM_THROUGHPUT": {"name": "EXTERNAL_MODEL_STREAM_THROUGHPUT", "type": "release", "description": "Sampler for token stream throughput for external models", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "EXTERNAL_MODEL_STREAM_THROUGHPUT", "rollout": "50", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "FAST_MULTILINE": {"name": "FAST_MULTILINE", "type": "experiment", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["vscode", "jetbrains", "windsurf", "windsurf-insiders"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "FAST_MULTILINE", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "FAST_SPEED_KILL_SWITCH": {"name": "FAST_SPEED_KILL_SWITCH", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["vscode"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "FAST_SPEED_KILL_SWITCH", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "FIREWORKS_ON_DEMAND_DEPLOYMENT": {"name": "FIREWORKS_ON_DEMAND_DEPLOYMENT", "type": "experiment", "description": "Switch traffic to on demand compute for fireworks", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "FIREWORKS_ON_DEMAND_DEPLOYMENT", "rollout": "25", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "GENERATE_CODEBASE_CLUSTERS": {"name": "GENERATE_CODEBASE_CLUSTERS", "type": "experiment", "description": "Whether or not to generate codebase clusters for context awareness.", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "devMode", "operator": "IN", "values": ["true", "True", "TRUE"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "GENERATE_CODEBASE_CLUSTERS", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "IMPLICIT_PLAN": {"name": "IMPLICIT_PLAN", "type": "operational", "description": "", "enabled": false, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "extensionVersion", "operator": "SEMVER_LT", "values": [], "value": "1.12.1", "caseInsensitive": false, "inverted": true}, {"contextName": "devMode", "operator": "IN", "values": ["true"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "IMPLICIT_PLAN", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "extensionVersion", "operator": "SEMVER_GT", "values": [], "value": "1.8.79", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "IMPLICIT_PLAN", "rollout": "35", "stickiness": "userId"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "IMPLICIT_TRAJECTORY_USE_CLIPBOARD": {"name": "IMPLICIT_TRAJECTORY_USE_CLIPBOARD", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "IMPLICIT_TRAJECTORY_USE_CLIPBOARD", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "IMPLICIT_TRAJECTORY_USE_INTELLISENSE": {"name": "IMPLICIT_TRAJECTORY_USE_INTELLISENSE", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "IMPLICIT_TRAJECTORY_USE_INTELLISENSE", "rollout": "100", "stickiness": "userId"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "IMPLICIT_USES_CLIPBOARD": {"name": "IMPLICIT_USES_CLIPBOARD", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "IMPLICIT_USES_CLIPBOARD", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "INCLUDE_PROMPT_COMPONENTS": {"name": "INCLUDE_PROMPT_COMPONENTS", "type": "experiment", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "extensionVersion", "operator": "SEMVER_LT", "values": [], "value": "1.16.2", "caseInsensitive": false, "inverted": true}], "parameters": {"groupId": "INCLUDE_PROMPT_COMPONENTS", "rollout": "100", "stickiness": "random"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "JETBRAINS_ENABLE_AUTOUPDATE": {"name": "JETBRAINS_ENABLE_AUTOUPDATE", "type": "experiment", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "JETBRAINS_ENABLE_AUTOUPDATE", "rollout": "75", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": true}, "JETBRAINS_ENABLE_ONBOARDING": {"name": "JETBRAINS_ENABLE_ONBOARDING", "type": "experiment", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "JETBRAINS_ENABLE_ONBOARDING", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "JETBRAINS_USE_COMMAND_DOCSTRING_GENERATION": {"name": "JETBRAINS_USE_COMMAND_DOCSTRING_GENERATION", "type": "experiment", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "JETBRAINS_USE_COMMAND_DOCSTRING_GENERATION", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "JETBRAINS_USE_LEXICAL_EDITOR": {"name": "JETBRAINS_USE_LEXICAL_EDITOR", "type": "experiment", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "os", "operator": "IN", "values": ["windows"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "JETBRAINS_USE_LEXICAL_EDITOR", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "os", "operator": "IN", "values": ["mac"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "JETBRAINS_USE_LEXICAL_EDITOR", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "os", "operator": "IN", "values": ["linux"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "JETBRAINS_USE_LEXICAL_EDITOR", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "KNOWLEDGE_BASE_PROMPT_FRACTION": {"name": "KNOWLEDGE_BASE_PROMPT_FRACTION", "type": "experiment", "description": "% of prompt that's filled with knowledge base items", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "autoCascadeMode", "operator": "IN", "values": ["true"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "KNOWLEDGE_BASE_PROMPT_FRACTION", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "LANGUAGE_SERVER_AUTO_RELOAD": {"name": "LANGUAGE_SERVER_AUTO_RELOAD", "type": "experiment", "description": "", "enabled": false, "strategies": [{"id": 0, "name": "default", "constraints": [{"contextName": "extensionVersion", "operator": "IN", "values": ["1.3.101"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "LANGUAGE_SERVER_VERSION": {"name": "LANGUAGE_SERVER_VERSION", "type": "operational", "description": "Experiment to override the language server version", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "extensionVersion", "operator": "IN", "values": ["1.15.1", "1.14.1"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "LANGUAGE_SERVER_VERSION", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "hotfix", "payload": {"type": "json", "value": "{\n    \"crc32c_linux_x64\": \"becc6dbc\",\n    \"crc32c_linux_arm\": \"47bde85f\",\n    \"crc32c_macos_x64\": \"b7706bc5\",\n    \"crc32c_macos_arm\": \"3c0aca33\",\n    \"crc32c_windows_x64\": \"5a8346c4\",\n    \"sha\": \"54ab3e65ecfa148b8e67b77f0a9fd95973b1ae0a\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "extensionVersion", "operator": "IN", "values": ["1.15.12"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "LANGUAGE_SERVER_VERSION", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "hotfix", "payload": {"type": "json", "value": "{\n    \"crc32c_linux_x64\": \"0c01ca3f\",\n    \"crc32c_linux_arm\": \"0afb0cd2\",\n    \"crc32c_macos_x64\": \"cebc622e\",\n    \"crc32c_macos_arm\": \"eab6e9c8\",\n    \"crc32c_windows_x64\": \"4f8b5f0d\",\n    \"sha\": \"55ba1c2d8bc6a1527d1b6f06f13a8a16fd42bbb7\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "extensionVersion", "operator": "IN", "values": ["1.15.7"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "LANGUAGE_SERVER_VERSION", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "hotfix", "payload": {"type": "json", "value": "{\n    \"crc32c_linux_x64\": \"2937712e\",\n    \"crc32c_linux_arm\": \"6a879648\",\n    \"crc32c_macos_x64\": \"9420a79a\",\n    \"crc32c_macos_arm\": \"36cbc8de\",\n    \"crc32c_windows_x64\": \"918d8861\",\n    \"sha\": \"2fec03f5c96bbfddf7da025d62e227cb10f6e8e5\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "extensionVersion", "operator": "IN", "values": ["1.15.9"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "LANGUAGE_SERVER_VERSION", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "hotfix", "payload": {"type": "json", "value": "{\n    \"crc32c_linux_x64\": \"f44c26d3\",\n    \"crc32c_linux_arm\": \"4340e2c5\",\n    \"crc32c_macos_x64\": \"005a5658\",\n    \"crc32c_macos_arm\": \"ad79f978\",\n    \"crc32c_windows_x64\": \"a8947bbf\",\n    \"sha\": \"2263ab292505638487fa9b6c6a988dcc02ee253e\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": true}, "LIMIT_PREFIX": {"name": "LIMIT_PREFIX", "type": "experiment", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "LIMIT_PREFIX", "rollout": "100", "stickiness": "random"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "LLAMA3_405B_KILL_SWITCH": {"name": "LLAMA3_405B_KILL_SWITCH", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "default", "constraints": [], "parameters": {}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "MIDDLE_MODE_TOKEN_VARIANT": {"name": "MIDDLE_MODE_TOKEN_VARIANT", "type": "experiment", "description": "", "enabled": false, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "MIDDLE_MODE_TOKEN_VARIANT", "rollout": "100", "stickiness": "random"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [{"name": "MIDDLE_MODE_2", "payload": {"type": "json", "value": "{\"mode_token\": \"<|middle_mode_2|>\"}"}, "enabled": false, "featureEnabled": false, "weight": 334, "weightType": "variable", "stickiness": "default", "overrides": []}, {"name": "MIDDLE_MODE_4", "payload": {"type": "json", "value": "{\"mode_token\": \"<|middle_mode_4|>\"}"}, "enabled": false, "featureEnabled": false, "weight": 333, "weightType": "variable", "stickiness": "default", "overrides": []}, {"name": "NO_MIDDLE_MODE", "payload": {"type": "json", "value": "{\"mode_token\": \"\"}"}, "enabled": false, "featureEnabled": false, "weight": 333, "weightType": "variable", "stickiness": "default", "overrides": []}], "dependencies": null, "impressionData": false}, "MIN_IDE_VERSION": {"name": "MIN_IDE_VERSION", "type": "operational", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "MIN_IDE_VERSION", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [{"name": "default", "payload": {"type": "string", "value": "codesandbox: \"1.0.0\""}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "variable", "stickiness": "default", "overrides": []}], "dependencies": null, "impressionData": false}, "MODEL_12471_TOKENS": {"name": "MODEL_12471_TOKENS", "type": "experiment", "description": "Varying context length for MODEL_12471", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "MODEL_12471_TOKENS", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [{"name": "2048_tokens", "payload": {"type": "string", "value": "2048"}, "enabled": false, "featureEnabled": false, "weight": 330, "weightType": "fix", "stickiness": "random", "overrides": []}, {"name": "3072_tokens", "payload": {"type": "string", "value": "3072"}, "enabled": false, "featureEnabled": false, "weight": 330, "weightType": "fix", "stickiness": "random", "overrides": []}, {"name": "4096_tokens", "payload": {"type": "string", "value": "4096"}, "enabled": false, "featureEnabled": false, "weight": 340, "weightType": "variable", "stickiness": "random", "overrides": []}], "dependencies": null, "impressionData": false}, "MODEL_14602_TOKENS": {"name": "MODEL_14602_TOKENS", "type": "experiment", "description": "Varying the context length for MODEL_14602 (3b autocomplete)", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "MODEL_14602_TOKENS", "rollout": "100", "stickiness": "random"}, "segments": null, "variants": [{"name": "2048_tokens", "payload": {"type": "string", "value": "2048"}, "enabled": false, "featureEnabled": false, "weight": 500, "weightType": "", "stickiness": "random", "overrides": null}, {"name": "3072_tokens", "payload": {"type": "string", "value": "3072"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "random", "overrides": null}, {"name": "4096_tokens", "payload": {"type": "string", "value": "4096"}, "enabled": false, "featureEnabled": false, "weight": 500, "weightType": "", "stickiness": "random", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "MODEL_15133_TOKENS": {"name": "MODEL_15133_TOKENS", "type": "experiment", "description": "varying the context length for MODEL_15133", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "devMode", "operator": "IN", "values": ["true"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "MODEL_15133_TOKENS", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "2048_tokens", "payload": {"type": "string", "value": "2048"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "default", "overrides": null}, {"name": "4096_tokens", "payload": {"type": "string", "value": "4096"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "MODEL_15133_TOKENS", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "4096_tokens", "payload": {"type": "string", "value": "4096"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "extensionVersion", "operator": "SEMVER_LT", "values": [], "value": "1.31.14", "caseInsensitive": false, "inverted": true}], "parameters": {"groupId": "MODEL_15133_TOKENS", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "2048", "payload": {"type": "string", "value": "2048"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}, {"name": "3072", "payload": {"type": "string", "value": "3072"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "default", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "MODEL_15133_TOKENS", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "2048", "payload": {"type": "string", "value": "2048"}, "enabled": false, "featureEnabled": false, "weight": 950, "weightType": "", "stickiness": "default", "overrides": null}, {"name": "4096", "payload": {"type": "string", "value": "4096"}, "enabled": false, "featureEnabled": false, "weight": 50, "weightType": "", "stickiness": "default", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "MODEL_15133_VARIANTS": {"name": "MODEL_15133_VARIANTS", "type": "experiment", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf", "windsurf-insiders", "windsurf-next"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "MODEL_15133_VARIANTS", "rollout": "100", "stickiness": "random"}, "segments": null, "variants": [{"name": "default", "payload": {"type": "", "value": ""}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "random", "overrides": null}, {"name": "pro", "payload": {"type": "string", "value": "pro"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "random", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "teamsMode", "operator": "IN", "values": ["true"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "MODEL_15133_VARIANTS", "rollout": "100", "stickiness": "random"}, "segments": null, "variants": [{"name": "default", "payload": {"type": "", "value": ""}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "random", "overrides": null}, {"name": "pro", "payload": {"type": "string", "value": "pro"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "random", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "MODEL_15133_VARIANTS", "rollout": "100", "stickiness": "random"}, "segments": null, "variants": [{"name": "default", "payload": {"type": "", "value": ""}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "random", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "MODEL_15302_TOKENS": {"name": "MODEL_15302_TOKENS", "type": "experiment", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "MODEL_15302_TOKENS", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "2048_tokens", "payload": {"type": "string", "value": "2048"}, "enabled": false, "featureEnabled": false, "weight": 334, "weightType": "", "stickiness": "default", "overrides": null}, {"name": "4096_tokens", "payload": {"type": "string", "value": "4096"}, "enabled": false, "featureEnabled": false, "weight": 333, "weightType": "", "stickiness": "default", "overrides": null}, {"name": "6144_tokens", "payload": {"type": "string", "value": "6144"}, "enabled": false, "featureEnabled": false, "weight": 333, "weightType": "", "stickiness": "default", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "MODEL_15335_TOKENS": {"name": "MODEL_15335_TOKENS", "type": "experiment", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "MODEL_15335_TOKENS", "rollout": "100", "stickiness": "random"}, "segments": null, "variants": [{"name": "4096_TOKEN", "payload": {"type": "string", "value": "4096"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "random", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "MODEL_15931_TOKENS": {"name": "MODEL_15931_TOKENS", "type": "experiment", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "MODEL_15931_TOKENS", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "2048_tokens", "payload": {"type": "string", "value": "2048"}, "enabled": false, "featureEnabled": false, "weight": 500, "weightType": "", "stickiness": "default", "overrides": null}, {"name": "4096_tokens", "payload": {"type": "string", "value": "4096"}, "enabled": false, "featureEnabled": false, "weight": 500, "weightType": "", "stickiness": "default", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "MODEL_8341_VARIANTS": {"name": "MODEL_8341_VARIANTS", "type": "experiment", "description": "", "enabled": false, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "MODEL_8341_VARIANTS", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [{"name": "crusoe", "payload": {"type": "string", "value": "crusoe"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "fix", "stickiness": "random", "overrides": []}, {"name": "default", "payload": {"type": "", "value": ""}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "variable", "stickiness": "random", "overrides": []}], "dependencies": null, "impressionData": false}, "MODEL_CASCADE_20064_VARIANTS": {"name": "MODEL_CASCADE_20064_VARIANTS", "type": "experiment", "description": "route traffic between variants of MODEL_CASCADE_20064", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-insiders"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "MODEL_CASCADE_20064_VARIANTS", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "default", "payload": {"type": "", "value": ""}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}, {"name": "iceland", "payload": {"type": "string", "value": "iceland"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "default", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "MODEL_CASCADE_20064_VARIANTS", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "crusoe-iceland", "payload": {"type": "string", "value": "iceland"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}, {"name": "crusoe-sc", "payload": {"type": "", "value": ""}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "default", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "MODEL_CASCADE_20065_VARIANTS": {"name": "MODEL_CASCADE_20065_VARIANTS", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "MODEL_CASCADE_20065_VARIANTS", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "default", "payload": {"type": "", "value": ""}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "default", "overrides": null}, {"name": "iceland", "payload": {"type": "string", "value": "iceland"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "MODEL_CASCADE_20071_VARIANTS": {"name": "MODEL_CASCADE_20071_VARIANTS", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-insiders"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "MODEL_CASCADE_20071_VARIANTS", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "default", "payload": {"type": "", "value": ""}, "enabled": false, "featureEnabled": false, "weight": 200, "weightType": "", "stickiness": "default", "overrides": null}, {"name": "sydney", "payload": {"type": "string", "value": "sydney"}, "enabled": false, "featureEnabled": false, "weight": 800, "weightType": "", "stickiness": "default", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-next"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "MODEL_CASCADE_20071_VARIANTS", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "default", "payload": {"type": "", "value": ""}, "enabled": false, "featureEnabled": false, "weight": 300, "weightType": "", "stickiness": "default", "overrides": null}, {"name": "sydney", "payload": {"type": "string", "value": "sydney"}, "enabled": false, "featureEnabled": false, "weight": 700, "weightType": "", "stickiness": "default", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "teamsMode", "operator": "IN", "values": ["true"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "MODEL_CASCADE_20071_VARIANTS", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "default", "payload": {"type": "", "value": ""}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "MODEL_CASCADE_20071_VARIANTS", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "default", "payload": {"type": "", "value": ""}, "enabled": false, "featureEnabled": false, "weight": 800, "weightType": "", "stickiness": "default", "overrides": null}, {"name": "sydney", "payload": {"type": "string", "value": "sydney"}, "enabled": false, "featureEnabled": false, "weight": 200, "weightType": "", "stickiness": "default", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "MODEL_CASCADE_20072_VARIANTS": {"name": "MODEL_CASCADE_20072_VARIANTS", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-insiders"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "MODEL_CASCADE_20072_VARIANTS", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "default", "payload": {"type": "", "value": ""}, "enabled": false, "featureEnabled": false, "weight": 200, "weightType": "", "stickiness": "default", "overrides": null}, {"name": "sydney", "payload": {"type": "string", "value": "sydney"}, "enabled": false, "featureEnabled": false, "weight": 800, "weightType": "", "stickiness": "default", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-next"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "MODEL_CASCADE_20072_VARIANTS", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "default", "payload": {"type": "", "value": ""}, "enabled": false, "featureEnabled": false, "weight": 300, "weightType": "", "stickiness": "default", "overrides": null}, {"name": "sydney", "payload": {"type": "string", "value": "sydney"}, "enabled": false, "featureEnabled": false, "weight": 700, "weightType": "", "stickiness": "default", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "teamsMode", "operator": "IN", "values": ["true"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "MODEL_CASCADE_20072_VARIANTS", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "default", "payload": {"type": "", "value": ""}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "MODEL_CASCADE_20072_VARIANTS", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "default", "payload": {"type": "", "value": ""}, "enabled": false, "featureEnabled": false, "weight": 700, "weightType": "", "stickiness": "default", "overrides": null}, {"name": "sydney", "payload": {"type": "string", "value": "sydney"}, "enabled": false, "featureEnabled": false, "weight": 300, "weightType": "", "stickiness": "default", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "MODEL_CHAT_11121_VARIANTS": {"name": "MODEL_CHAT_11121_VARIANTS", "type": "experiment", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "MODEL_CHAT_11121_VARIANTS", "rollout": "100", "stickiness": "random"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [{"name": "crusoe", "payload": {"type": "string", "value": "crusoe"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "fix", "stickiness": "random", "overrides": []}, {"name": "default", "payload": {"type": "", "value": ""}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "variable", "stickiness": "random", "overrides": []}], "dependencies": null, "impressionData": false}, "MODEL_CHAT_12119_VARIANTS": {"name": "MODEL_CHAT_12119_VARIANTS", "type": "experiment", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "MODEL_CHAT_12119_VARIANTS", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "default", "payload": {"type": "", "value": ""}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "default", "overrides": null}, {"name": "iceland", "payload": {"type": "string", "value": "iceland"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [{"name": "default", "payload": {"type": "", "value": ""}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "variable", "stickiness": "default", "overrides": []}, {"name": "speculative-13175", "payload": {"type": "string", "value": "speculative-13175"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "fix", "stickiness": "default", "overrides": []}], "dependencies": null, "impressionData": false}, "MODEL_CHAT_15305_TOKENS": {"name": "MODEL_CHAT_15305_TOKENS", "type": "experiment", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "MODEL_CHAT_15305_TOKENS", "rollout": "100", "stickiness": "random"}, "segments": null, "variants": [{"name": "4096_tokens", "payload": {"type": "string", "value": "4096"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "random", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": true}, "MODEL_CHAT_15305_VARIANTS": {"name": "MODEL_CHAT_15305_VARIANTS", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "MODEL_CHAT_15305_VARIANTS", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "default", "payload": {"type": "", "value": ""}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "MODEL_CHAT_15476_TOKENS": {"name": "MODEL_CHAT_15476_TOKENS", "type": "experiment", "description": "Varying the autocomplete context length for MODEL_CHAT_15476 (experimental 10b combined autocomplete and supercomplete)", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "MODEL_CHAT_15476_TOKENS", "rollout": "100", "stickiness": "random"}, "segments": null, "variants": [{"name": "2048_tokens", "payload": {"type": "string", "value": "2048"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "random", "overrides": null}, {"name": "2560_tokens", "payload": {"type": "string", "value": "2560"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "random", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": true}, "MODEL_CHAT_15600_TOKENS": {"name": "MODEL_CHAT_15600_TOKENS", "type": "experiment", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "MODEL_CHAT_15600_TOKENS", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "4096_tokens", "payload": {"type": "string", "value": "4096"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}, {"name": "6144_tokens", "payload": {"type": "string", "value": "6144"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "default", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": true}, "MODEL_CHAT_15729_TOKENS": {"name": "MODEL_CHAT_15729_TOKENS", "type": "experiment", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "MODEL_CHAT_15729_TOKENS", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "4096_tokens", "payload": {"type": "string", "value": "4096"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "default", "overrides": null}, {"name": "5120_tokens", "payload": {"type": "string", "value": "5120"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}, {"name": "6144_tokens", "payload": {"type": "string", "value": "6144"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "default", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": true}, "MODEL_CHAT_16579_CRUSOE_TOKENS": {"name": "MODEL_CHAT_16579_CRUSOE_TOKENS", "type": "experiment", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "MODEL_CHAT_16579_CRUSOE_TOKENS", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "2000_tokens", "payload": {"type": "", "value": ""}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "default", "overrides": null}, {"name": "4000_tokens", "payload": {"type": "string", "value": "4000"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}, {"name": "6000_tokens", "payload": {"type": "string", "value": "6000"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "default", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": true}, "MODEL_CHAT_16579_TOKENS": {"name": "MODEL_CHAT_16579_TOKENS", "type": "experiment", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "devMode", "operator": "IN", "values": ["true"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "MODEL_CHAT_16579_TOKENS", "rollout": "100", "stickiness": "random"}, "segments": null, "variants": [{"name": "4096_tokens", "payload": {"type": "string", "value": "4096"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "random", "overrides": null}, {"name": "5120_tokens", "payload": {"type": "string", "value": "5120"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "random", "overrides": null}, {"name": "6144_tokens", "payload": {"type": "string", "value": "6144"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "random", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "MODEL_CHAT_16579_TOKENS", "rollout": "100", "stickiness": "random"}, "segments": null, "variants": [{"name": "4096_tokens", "payload": {"type": "string", "value": "4096"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "random", "overrides": null}, {"name": "5120_tokens", "payload": {"type": "string", "value": "5120"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "random", "overrides": null}, {"name": "6144_tokens", "payload": {"type": "string", "value": "6144"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "random", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": true}, "MODEL_CHAT_16801_TOKENS": {"name": "MODEL_CHAT_16801_TOKENS", "type": "experiment", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "MODEL_CHAT_16801_TOKENS", "rollout": "100", "stickiness": "random"}, "segments": null, "variants": [{"name": "4096_tokens", "payload": {"type": "string", "value": "4096"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "random", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": true}, "MODEL_CHAT_18468_TOKENS": {"name": "MODEL_CHAT_18468_TOKENS", "type": "experiment", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "MODEL_CHAT_18468_TOKENS", "rollout": "100", "stickiness": "random"}, "segments": null, "variants": [{"name": "4096_tokens", "payload": {"type": "string", "value": "4096"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "random", "overrides": null}, {"name": "6144_tokens", "payload": {"type": "string", "value": "6144"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "random", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": true}, "MODEL_CHAT_18805_TOKENS": {"name": "MODEL_CHAT_18805_TOKENS", "type": "experiment", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "MODEL_CHAT_18805_TOKENS", "rollout": "100", "stickiness": "random"}, "segments": null, "variants": [{"name": "5120_tokens", "payload": {"type": "string", "value": "5120"}, "enabled": false, "featureEnabled": false, "weight": 500, "weightType": "", "stickiness": "random", "overrides": null}, {"name": "6144_tokens", "payload": {"type": "string", "value": "6144"}, "enabled": false, "featureEnabled": false, "weight": 500, "weightType": "", "stickiness": "random", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": true}, "MODEL_CHAT_19040_TOKENS": {"name": "MODEL_CHAT_19040_TOKENS", "type": "experiment", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "MODEL_CHAT_19040_TOKENS", "rollout": "100", "stickiness": "sessionId"}, "segments": null, "variants": [{"name": "6144_tokens", "payload": {"type": "string", "value": "6144"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "sessionId", "overrides": null}, {"name": "8192_tokens", "payload": {"type": "string", "value": "8192"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "sessionId", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": true}, "MODEL_CHAT_19484_TOKENS": {"name": "MODEL_CHAT_19484_TOKENS", "type": "experiment", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "MODEL_CHAT_19484_TOKENS", "rollout": "100", "stickiness": "random"}, "segments": null, "variants": [{"name": "4096_tokens", "payload": {"type": "string", "value": "4096"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "random", "overrides": null}, {"name": "6144_tokens", "payload": {"type": "string", "value": "6144"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "random", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": true}, "MODEL_CHAT_19820_TOKENS": {"name": "MODEL_CHAT_19820_TOKENS", "type": "experiment", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "MODEL_CHAT_19820_TOKENS", "rollout": "100", "stickiness": "sessionId"}, "segments": null, "variants": [{"name": "16384_tokens", "payload": {"type": "string", "value": "16384"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "sessionId", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": true}, "MODEL_CHAT_19821_TOKENS": {"name": "MODEL_CHAT_19821_TOKENS", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-insiders"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "MODEL_CHAT_19821_TOKENS", "rollout": "100", "stickiness": "sessionId"}, "segments": null, "variants": [{"name": "12288_tokens", "payload": {"type": "string", "value": "12288"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "sessionId", "overrides": null}, {"name": "14336_tokens", "payload": {"type": "string", "value": "14336"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "sessionId", "overrides": null}, {"name": "16384_tokens", "payload": {"type": "string", "value": "16384"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "sessionId", "overrides": null}, {"name": "18432_tokens", "payload": {"type": "string", "value": "18432"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "sessionId", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf"], "value": "", "caseInsensitive": false, "inverted": false}, {"contextName": "planName", "operator": "IN", "values": ["Free"], "value": "", "caseInsensitive": false, "inverted": true}], "parameters": {"groupId": "MODEL_CHAT_19821_TOKENS", "rollout": "100", "stickiness": "sessionId"}, "segments": null, "variants": [{"name": "14336_tokens", "payload": {"type": "string", "value": "14336"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "sessionId", "overrides": null}, {"name": "16384_tokens", "payload": {"type": "string", "value": "16384"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "sessionId", "overrides": null}, {"name": "18432_tokens", "payload": {"type": "string", "value": "18432"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "sessionId", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-next"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "MODEL_CHAT_19821_TOKENS", "rollout": "100", "stickiness": "sessionId"}, "segments": null, "variants": [{"name": "12288_tokens", "payload": {"type": "string", "value": "12288"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "sessionId", "overrides": null}, {"name": "14336_tokens", "payload": {"type": "string", "value": "14336"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "sessionId", "overrides": null}, {"name": "16384_tokens", "payload": {"type": "string", "value": "16384"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "sessionId", "overrides": null}, {"name": "18432_tokens", "payload": {"type": "string", "value": "18432"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "sessionId", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "MODEL_CHAT_19821_TOKENS", "rollout": "100", "stickiness": "sessionId"}, "segments": null, "variants": [{"name": "12288_tokens", "payload": {"type": "string", "value": "12288"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "sessionId", "overrides": null}, {"name": "14336_tokens", "payload": {"type": "string", "value": "14336"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "sessionId", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": true}, "MODEL_CHAT_19821_VARIANTS": {"name": "MODEL_CHAT_19821_VARIANTS", "type": "experiment", "description": "", "enabled": false, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-insiders"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "MODEL_CHAT_19821_VARIANTS", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "highprio", "payload": {"type": "string", "value": "highprio"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-next"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "MODEL_CHAT_19821_VARIANTS", "rollout": "100", "stickiness": "sessionId"}, "segments": null, "variants": [{"name": "highprio", "payload": {"type": "string", "value": "highprio"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "sessionId", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf"], "value": "", "caseInsensitive": false, "inverted": false}, {"contextName": "country", "operator": "IN", "values": ["india", "china"], "value": "", "caseInsensitive": false, "inverted": false}, {"contextName": "loadScore", "operator": "NUM_GT", "values": [], "value": "95", "caseInsensitive": false, "inverted": false}, {"contextName": "planName", "operator": "IN", "values": ["Pro"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "MODEL_CHAT_19821_VARIANTS", "rollout": "100", "stickiness": "userId"}, "segments": null, "variants": [{"name": "default", "payload": {"type": "string", "value": "default"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "userId", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf"], "value": "", "caseInsensitive": false, "inverted": false}, {"contextName": "planName", "operator": "IN", "values": ["Free"], "value": "", "caseInsensitive": false, "inverted": true}], "parameters": {"groupId": "MODEL_CHAT_19821_VARIANTS", "rollout": "100", "stickiness": "sessionId"}, "segments": null, "variants": [{"name": "highprio", "payload": {"type": "string", "value": "highprio"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "sessionId", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf"], "value": "", "caseInsensitive": false, "inverted": false}, {"contextName": "continent", "operator": "IN", "values": ["North America", "Europe"], "value": "", "caseInsensitive": false, "inverted": false}, {"contextName": "extensionVersion", "operator": "SEMVER_LT", "values": [], "value": "1.40.2", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "MODEL_CHAT_19821_VARIANTS", "rollout": "100", "stickiness": "sessionId"}, "segments": null, "variants": [{"name": "highprio", "payload": {"type": "string", "value": "highprio"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "sessionId", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "MODEL_CHAT_19821_VARIANTS", "rollout": "100", "stickiness": "sessionId"}, "segments": null, "variants": [{"name": "default", "payload": {"type": "", "value": ""}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "sessionId", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": true}, "MODEL_CHAT_19822_TOKENS": {"name": "MODEL_CHAT_19822_TOKENS", "type": "experiment", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "MODEL_CHAT_19822_TOKENS", "rollout": "100", "stickiness": "sessionId"}, "segments": null, "variants": [{"name": "16384_tokens", "payload": {"type": "string", "value": "16384"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "sessionId", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": true}, "MODEL_CHAT_20706_TOKENS": {"name": "MODEL_CHAT_20706_TOKENS", "type": "experiment", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "MODEL_CHAT_20706_TOKENS", "rollout": "100", "stickiness": "random"}, "segments": null, "variants": [{"name": "10240_tokens", "payload": {"type": "string", "value": "10240"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "random", "overrides": null}, {"name": "4096_tokens", "payload": {"type": "string", "value": "4096"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "random", "overrides": null}, {"name": "6144_tokens", "payload": {"type": "string", "value": "6144"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "random", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": true}, "MODEL_CHAT_21779-cumulative-prompt-config": {"name": "MODEL_CHAT_21779-cumulative-prompt-config", "type": "experiment", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "MODEL_CHAT_21779-cumulative-prompt-config", "rollout": "100", "stickiness": "random"}, "segments": null, "variants": [{"name": "default_16k", "payload": {"type": "json", "value": "{\n    \"persistent_context_multiplier\": 0.33,\n    \"persistent_active_document_multiplier\": 0.5,\n    \"persistent_open_docs_multiplier\": 0.25,\n    \"persistent_max_tokens_per_open_doc\": 2048,\n    \"persistent_document_suffix_frac\": 0.5,\n    \"persistent_max_ccis_considered\": 25,\n    \"trajectory_context_multiplier\": 0.6,\n    \"trajectory_refresh_threshold_multiplier\": 0.9,\n    \"trajectory_truncation_multiplier\": 0.5,\n    \"ephemeral_context_multiplier\": 0.07,\n    \"intent_reservation_tokens\": 256,\n    \"ephemeral_active_document_multiplier\": 0.5,\n    \"ephemeral_max_ccis_considered\": 25,\n    \"ephemeral_document_suffix_frac\": 0.5\n}"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "random", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": true}, "MODEL_CHAT_21779_TOKENS": {"name": "MODEL_CHAT_21779_TOKENS", "type": "experiment", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "MODEL_CHAT_21779_TOKENS", "rollout": "100", "stickiness": "random"}, "segments": null, "variants": [{"name": "18432_tokens", "payload": {"type": "string", "value": "18432"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "random", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": true}, "MODEL_CHAT_22798-cumulative-prompt-config": {"name": "MODEL_CHAT_22798-cumulative-prompt-config", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "MODEL_CHAT_22798-cumulative-prompt-config", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "default_16k", "payload": {"type": "json", "value": "{\n    \"persistent_context_multiplier\": 0.25,\n    \"persistent_active_document_multiplier\": 0.5,\n    \"persistent_open_docs_multiplier\": 0.25,\n    \"persistent_max_tokens_per_open_doc\": 2048,\n    \"persistent_document_suffix_frac\": 0.5,\n    \"persistent_max_ccis_considered\": 25,\n    \"trajectory_context_multiplier\": 0.6,\n    \"trajectory_refresh_threshold_multiplier\": 0.9,\n    \"trajectory_truncation_multiplier\": 0.5,\n    \"ephemeral_context_multiplier\": 0.15,\n    \"intent_reservation_tokens\": 256,\n    \"ephemeral_active_document_multiplier\": 0.5,\n    \"ephemeral_max_ccis_considered\": 25,\n    \"ephemeral_document_suffix_frac\": 0.5\n}"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "MODEL_CHAT_23310_TOKENS": {"name": "MODEL_CHAT_23310_TOKENS", "type": "experiment", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "MODEL_CHAT_23310_TOKENS", "rollout": "100", "stickiness": "sessionId"}, "segments": null, "variants": [{"name": "16384_tokens", "payload": {"type": "string", "value": "16384"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "sessionId", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": true}, "MODEL_LLAMA_3_1_70B_INSTRUCT_LONG_CONTEXT_VARIANTS": {"name": "MODEL_LLAMA_3_1_70B_INSTRUCT_LONG_CONTEXT_VARIANTS", "type": "experiment", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "MODEL_LLAMA_3_1_70B_INSTRUCT_LONG_CONTEXT_VARIANTS", "rollout": "100", "stickiness": "random"}, "segments": null, "variants": [{"name": "default", "payload": {"type": "", "value": ""}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "random", "overrides": null}, {"name": "llama-3-1-crusoe-sc", "payload": {"type": "string", "value": "llama-3-1-crusoe-sc"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "random", "overrides": null}, {"name": "llama-3-1-iceland", "payload": {"type": "string", "value": "llama-3-1"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "random", "overrides": null}, {"name": "llama-3-3", "payload": {"type": "string", "value": "llama-3-3"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "random", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "MODEL_LLAMA_3_1_70B_INSTRUCT_VARIANTS": {"name": "MODEL_LLAMA_3_1_70B_INSTRUCT_VARIANTS", "type": "experiment", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "MODEL_LLAMA_3_1_70B_INSTRUCT_VARIANTS", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "default", "payload": {"type": "", "value": ""}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "default", "overrides": null}, {"name": "iceland", "payload": {"type": "string", "value": "iceland"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "MODEL_NOTIFICATIONS": {"name": "MODEL_NOTIFICATIONS", "type": "release", "description": "It's used to display a warning icon next to a model name in the model dropdown with a notification message on hover. To add a model and message update the affected_models variant payload. This flag should only be changed by on-call engineers.\n\nPayload format:\n{\n    \"model_notifications\": [\n            {\n                 \"model\": \"MODEL_CHAT_O1_MINI\",\n                 \"message\": \"Degraded performance\"\n            }\n    ]\n}", "enabled": false, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "MODEL_NOTIFICATIONS", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "insiders", "payload": {"type": "json", "value": "{\n  \"model_notifications\": [\n    {\n      \"model\": \"MODEL_GOOGLE_GEMINI_2_5_PRO\",\n      \"message\": \"Google outage, may experience errors\"\n    },\n    {\n      \"model\": \"MODEL_GOOGLE_GEMINI_2_5_FLASH_PREVIEW_05_20\",\n      \"message\": \"Google outage, may experience errors\"\n    },\n    {\n      \"model\": \"M<PERSON>EL_GOOGLE_GEMINI_2_0_FLASH\",\n      \"message\": \"Google outage, may experience errors\"\n    },\n    {\n      \"model\": \"M<PERSON><PERSON>_GOOGLE_GEMINI_2_5_FLASH_PREVIEW_05_20_THINKING\",\n      \"message\": \"Google outage, may experience errors\"\n    }\n  ]\n}"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "MODEL_SELECTOR_ANTHROPIC_API_PRICING": {"name": "MODEL_SELECTOR_ANTHROPIC_API_PRICING", "type": "release", "description": "", "enabled": false, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-insiders"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "MODEL_SELECTOR_ANTHROPIC_API_PRICING", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "gpt-4.1", "payload": {"type": "string", "value": "GPT-4.1 is available for no credit cost between April 14 and April 28."}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "MODEL_SELECTOR_NUX_COPY": {"name": "MODEL_SELECTOR_NUX_COPY", "type": "release", "description": "", "enabled": false, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "MODEL_SELECTOR_NUX_COPY", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "gpt-4.1", "payload": {"type": "string", "value": "GPT-4.1 is available for no credit cost between April 14 and April 28."}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "MQUERY_SCORER_WITH_FALLBACK": {"name": "MQUERY_SCORER_WITH_FALLBACK", "type": "release", "description": "", "enabled": false, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "extensionVersion", "operator": "SEMVER_LT", "values": [], "value": "1.12.1", "caseInsensitive": false, "inverted": true}, {"contextName": "teamsMode", "operator": "IN", "values": ["true"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "MQUERY_SCORER_WITH_FALLBACK", "rollout": "50", "stickiness": "random"}, "segments": null, "variants": [{"name": "1s", "payload": {"type": "string", "value": "1000"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "random", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "MULTILINE_MODEL_THRESHOLD": {"name": "MULTILINE_MODEL_THRESHOLD", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "MULTILINE_MODEL_THRESHOLD", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [{"name": "THRESHOLD_04", "payload": {"type": "json", "value": "{\"threshold\": 0.4}"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "fix", "stickiness": "default", "overrides": []}, {"name": "THRESHOLD_045", "payload": {"type": "json", "value": "{\"threshold\": 0.45}"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "fix", "stickiness": "default", "overrides": []}, {"name": "THRESHOLD_05", "payload": {"type": "json", "value": "{\"threshold\": 0.5}"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "fix", "stickiness": "default", "overrides": []}, {"name": "THRESHOLD_055", "payload": {"type": "json", "value": "{\"threshold\": 0.55}"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "variable", "stickiness": "default", "overrides": []}, {"name": "THRESHOLD_06", "payload": {"type": "json", "value": "{\"threshold\": 0.6}"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "fix", "stickiness": "default", "overrides": []}], "dependencies": null, "impressionData": false}, "NON_TEAMS_KILL_SWITCH": {"name": "NON_TEAMS_KILL_SWITCH", "type": "release", "description": "", "enabled": false, "strategies": [{"id": 0, "name": "default", "constraints": [{"contextName": "teamsMode", "operator": "NOT_IN", "values": ["true"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "NO_SAMPLER_EARLY_STOP": {"name": "NO_SAMPLER_EARLY_STOP", "type": "experiment", "description": "", "enabled": false, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "NO_SAMPLER_EARLY_STOP", "rollout": "50", "stickiness": "random"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "ONLY_MULTILINE": {"name": "ONLY_MULTILINE", "type": "experiment", "description": "", "enabled": false, "strategies": [], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "OPEN_UNIVERSITY_ON_STARTUP": {"name": "OPEN_UNIVERSITY_ON_STARTUP", "type": "release", "description": "", "enabled": false, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "teamsMode", "operator": "NOT_IN", "values": ["true", "True", "1"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "OPEN_UNIVERSITY_ON_STARTUP", "rollout": "50", "stickiness": "userId"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "OTHER_DOCUMENTS": {"name": "OTHER_DOCUMENTS", "type": "experiment", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "extensionVersion", "operator": "SEMVER_LT", "values": [], "value": "1.16.1", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "OTHER_DOCUMENTS", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "PERSIST_CODE_TRACKER": {"name": "PERSIST_CODE_TRACKER", "type": "experiment", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "default", "constraints": [], "parameters": {}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "PIN_RECENT_FILES": {"name": "PIN_RECENT_FILES", "type": "experiment", "description": "Force pinning the active document and open documents to the context for retrieval", "enabled": false, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "extensionVersion", "operator": "SEMVER_GT", "values": [], "value": "1.20.9", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "PIN_RECENT_FILES", "rollout": "20", "stickiness": "default"}, "segments": null, "variants": [{"name": "1", "payload": {"type": "string", "value": "1"}, "enabled": false, "featureEnabled": false, "weight": 500, "weightType": "", "stickiness": "default", "overrides": null}, {"name": "5", "payload": {"type": "string", "value": "5"}, "enabled": false, "featureEnabled": false, "weight": 500, "weightType": "", "stickiness": "default", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "POST_APPLY_DECORATION_AUTOCOMPLETE": {"name": "POST_APPLY_DECORATION_AUTOCOMPLETE", "type": "release", "description": "Green highlighting after all accepts", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "POST_APPLY_DECORATION_AUTOCOMPLETE", "rollout": "100", "stickiness": "userId"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "PROFILING_TELEMETRY_SAMPLE_RATE": {"name": "PROFILING_TELEMETRY_SAMPLE_RATE", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "PROFILING_TELEMETRY_SAMPLE_RATE", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "WindsurfLS", "payload": {"type": "json", "value": "{\n  \"memory_usage_to_sample_rate\": {\n    \"0.1\": 0.0005,\n    \"0.5\": 0.005,\n    \"1\": 0.05,\n    \"10\": 0.5\n  }\n}"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "PRUNE_BAD_INLINE_FIM": {"name": "PRUNE_BAD_INLINE_FIM", "type": "experiment", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "default", "constraints": [], "parameters": {}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "QUICK_ACTIONS_WHITELIST_REGEX": {"name": "QUICK_ACTIONS_WHITELIST_REGEX", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "QUICK_ACTIONS_WHITELIST_REGEX", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "imports", "payload": {"type": "string", "value": ".*import.*"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "R2_LANGUAGE_SERVER_DOWNLOAD": {"name": "R2_LANGUAGE_SERVER_DOWNLOAD", "type": "experiment", "description": "Whether to download from Cloudfare R2 or Github Releases", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "extensionVersion", "operator": "SEMVER_LT", "values": [], "value": "1.16.11", "caseInsensitive": false, "inverted": true}], "parameters": {"groupId": "R2_LANGUAGE_SERVER_DOWNLOAD", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "RANGE_TRACKING": {"name": "RANGE_TRACKING", "type": "experiment", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "RANGE_TRACKING", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "RECORD_PRODUCT_EVENT": {"name": "RECORD_PRODUCT_EVENT", "type": "release", "description": "Whether to log a certain product event.", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "eventName", "operator": "IN", "values": ["SUPERCOMPLETE_ERROR_GETTING_RESPONSE", "TAB_JUMP_ERROR_GETTING_RESPONSE", "TAB_JUMP_ERROR_UI_RENDERED", "SUPERCOMPLETE_NO_RESPONSE", "TAB_JUMP_NO_RESPONSE"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "RECORD_PRODUCT_EVENT", "rollout": "100", "stickiness": "random"}, "segments": null, "variants": []}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "eventName", "operator": "IN", "values": ["SUPERCOMPLETE_CACHE_HIT", "SUPERCOMPLETE_REQUEST_SUCCEEDED", "SUPERCOMPLETE_FILTERED", "TAB_JUMP_PROCESSING_COMPLETE", "TAB_JUMP_FILTERED", "TAB_JUMP_CACHE_HIT"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "RECORD_PRODUCT_EVENT", "rollout": "20", "stickiness": "random"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "RECORD_TAB_SLOW_LS": {"name": "RECORD_TAB_SLOW_LS", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "avgLatency", "operator": "NUM_GTE", "values": [], "value": "100", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "RECORD_TAB_SLOW_LS", "rollout": "50", "stickiness": "random"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "REORDER_CONTEXT_PROMPT": {"name": "REORDER_CONTEXT_PROMPT", "type": "experiment", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "REORDER_CONTEXT_PROMPT", "rollout": "100", "stickiness": "random"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "RUN_RESEARCH_STATE_PROVIDER": {"name": "RUN_RESEARCH_STATE_PROVIDER", "type": "operational", "description": "", "enabled": false, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "extensionVersion", "operator": "SEMVER_GT", "values": [], "value": "1.8.63", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "RUN_RESEARCH_STATE_PROVIDER", "rollout": "5", "stickiness": "userId"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "SEMANTIC_CLEANUP_DIFF_KILL_SWITCH": {"name": "SEMANTIC_CLEANUP_DIFF_KILL_SWITCH", "type": "release", "description": "", "enabled": false, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "SEMANTIC_CLEANUP_DIFF_KILL_SWITCH", "rollout": "0", "stickiness": "userId"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "SENTRY": {"name": "SENTRY", "type": "operational", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "sentry<PERSON>n", "operator": "IN", "values": ["https://<EMAIL>/4507613429563392"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "SENTRY", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "experimental_always", "payload": {"type": "json", "value": "{\n  \"sample_rate\": 0.001,\n  \"procedure_to_sample_rate\": {\n    \"/exa.api_server_pb.ApiServerService/RecordCortexTrajectoryStep\": 0.00001,\n    \"/exa.api_server_pb.ApiServerService/RecordCortexTrajectory\": 0.00001,\n    \"/exa.api_server_pb.ApiServerService/CaptureCode\": 0.00001,\n    \"/exa.api_server_pb.ApiServerService/GetEmbeddings\": 0.0001,\n    \"/exa.auth_pb.AuthService/GetUserJwt\": 0.0001,\n    \"/exa.api_server_pb.ApiServerService/GetChatMessage\": 1.0,\n    \"/exa.api_server_pb.ApiServerService/ValidateWindsurfJSAppProjectName\": 1.0,\n    \"/exa.api_server_pb.ApiServerService/DeployWindsurfJSApp\": 1.0,\n    \"/exa.api_server_pb.ApiServerService/DeleteWindsurfJSApp\": 1.0,\n    \"/exa.api_server_pb.ApiServerService/GetWindsurfJSAppDeploymentClaimStatus\": 1.0,\n    \"/exa.api_server_pb.ApiServerService/GetWindsurfJSAppDeploymentsByProjectId\": 1.0,\n    \"/exa.api_server_pb.ApiServerService/GetWindsurfJSApps\": 1.0,\n    \"/exa.api_server_pb.ApiServerService/GetDeploymentProviderProjectNameByProjectId\": 1.0,\n    \"/exa.api_server_pb.ApiServerService/GetWindsurfJSAppDeploymentStatusesByProjectId\": 1.0,\n    \"/exa.api_server_pb.ApiServerService/GetWindsurfJSAppDeployment\": 1.0,\n    \"/exa.seat_management_pb.SeatManagementService.GetUserStatus\": 1.0\n  },\n  \"error_match_to_sample_rate\": {\n    \"is full, dropping record\": 0.00001,\n    \"rate limit exceeded for model\": 0.01,\n    \"missing user JWT for inference API server\": 0.0001,\n    \"user is disabled by team\": 0.00001,\n    \"team subscription is not active\": 0.00001,\n    \"user is not approved by team admin\": 0.00001,\n    \"edit summary is empty\": 0.0001,\n    \"user not authorized\": 0.00001,\n    \"empty property in unleash context\": 0.001,\n    \"invalid api key\": 0.001,\n    \"429 Too Many Requests\": 0.001,\n    \"you have been banned\": 0.001,\n    \"model API client for API_PROVIDER_OPENAI\": 0.001,\n    \"metadata.api_key: value length must be at least 1 characters\": 0.0001,\n    \"failed to unmarshal reasoning items\": 0.001\n  }\n}"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "sentry<PERSON>n", "operator": "IN", "values": ["https://<EMAIL>/4507789198753792", "https://<EMAIL>/4507463146012672", "https://<EMAIL>/4507579865366528"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "SENTRY", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "always", "payload": {"type": "json", "value": "{\n  \"sample_rate\": 0.0001,\n  \"error_match_to_sample_rate\": {\n    \"edit summary is empty\": 0.0,\n    \"failed to check terminal shell support\": 1.0\n  }\n}"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "sentry<PERSON>n", "operator": "IN", "values": ["https://<EMAIL>/4508406039183360"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "SENTRY", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "always", "payload": {"type": "json", "value": "{\n  \"sample_rate\": 0.001,\n  \"error_match_to_sample_rate\": {\n    \"edit summary is empty\": 0.0,\n    \"INTERNAL_ERROR; received from peer\": 1.0,\n    \"protocol error\": 1.0,\n    \"cascade executor error\": 0.1,\n    \"reactive component\": 0.0001\n  }\n}"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "sentry<PERSON>n", "operator": "IN", "values": ["https://<EMAIL>/4509121596358656"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "SENTRY", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "experimental_always", "payload": {"type": "json", "value": "{\n  \"sample_rate\": 1.0,\n  \"procedure_to_sample_rate\": {\n    \n  },\n  \"error_match_to_sample_rate\": {\n   \n  }\n}"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "sentry<PERSON>n", "operator": "IN", "values": ["https://<EMAIL>/4507737596690432"], "value": "", "caseInsensitive": false, "inverted": false}, {"contextName": "ide", "operator": "IN", "values": ["windsurf", "windsurf-insiders", "windsurf-next"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "SENTRY", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "always", "payload": {"type": "json", "value": "{\n  \"sample_rate\": 0.9\n}"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "SHOULD_SHOW_DEBUG_INFO_WIDGET": {"name": "SHOULD_SHOW_DEBUG_INFO_WIDGET", "type": "experiment", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "SHOULD_SHOW_DEBUG_INFO_WIDGET", "rollout": "0", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "SHOW_API_PRICING_CREDITS_USED": {"name": "SHOW_API_PRICING_CREDITS_USED", "type": "release", "description": "This will display a banner at the end of every user message to notify the user how many credits a message had taken. It is used for API Pricing.", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "SHOW_API_PRICING_CREDITS_USED", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "SINGLE_COMPLETION": {"name": "SINGLE_COMPLETION", "type": "experiment", "description": "", "enabled": false, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "SINGLE_COMPLETION", "rollout": "50", "stickiness": "random"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "SKIP_CONSISTENCY_MANAGER": {"name": "SKIP_CONSISTENCY_MANAGER", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["vscode"], "value": "", "caseInsensitive": false, "inverted": false}, {"contextName": "prereleaseMode", "operator": "IN", "values": ["false"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "SKIP_CONSISTENCY_MANAGER", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "SNAPSHOT_TO_STEP_OPTIONS_OVERRIDE": {"name": "SNAPSHOT_TO_STEP_OPTIONS_OVERRIDE", "type": "release", "description": "WARNING: \n\nIt is very easy to set incompatible set of override values that will immediately cause errors. If you change these settings, you must monitor sentry for config validation errors.\n\n\nThe correct format looks like a dict from string:string. The first string is a CORTEX_TRAJECTORY_TYPE enum name, and the second string is a serialized json corresponding to a cortex_pb.SnapshotToStepOptions proto message for that trajectory type. Be very, very careful with escaping the right characters here.\n\nThe proto set here will be merged with whatever the base proto is using default proto.Merge logic. Please check the proto definition and understand which fields are marked optional to be sure you understand the behavior. \n\nMost notably, step_type_allow_list is append only, and the \"zero-value\" will only overwrite the base config if the field is marked \"optional\".\n\n{\n  \"CORTEX_TRAJECTORY_TYPE_USER_MAINLINE\": \"{\\\"code_step_creation_options\\\":{\\\"include_original_content\\\":true},\\\"view_file_step_creation_options\\\":{\\\"include_raw_content\\\":true}}\",\n  \"CORTEX_TRAJECTORY_TYPE_CASCADE\": \"{\\\"viewed_file_tracker_config\\\":{\\\"max_steps_per_checkpoint\\\":1}, \\\"step_type_allow_list\\\":[\\\"CORTEX_STEP_TYPE_VIEW_FILE\\\"]}\"\n}\n", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-insiders"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "SNAPSHOT_TO_STEP_OPTIONS_OVERRIDE", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "more_view_file_telemetry", "payload": {"type": "json", "value": "{\n  \"CORTEX_TRAJECTORY_TYPE_USER_MAINLINE\": \"{\\\"code_step_creation_options\\\":{\\\"include_original_content\\\":true},\\\"view_file_step_creation_options\\\":{\\\"include_raw_content\\\":true}}\",\n  \"CORTEX_TRAJECTORY_TYPE_CASCADE\": \"{\\\"viewed_file_tracker_config\\\":{\\\"max_steps_per_checkpoint\\\":1}, \\\"step_type_allow_list\\\":[\\\"CORTEX_STEP_TYPE_VIEW_FILE\\\"]}\",\n  \"CORTEX_TRAJECTORY_TYPE_INTERACTIVE_CASCADE\": \"{}\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf"], "value": "", "caseInsensitive": false, "inverted": false}, {"contextName": "ideVersion", "operator": "SEMVER_LT", "values": [], "value": "1.5.6", "caseInsensitive": false, "inverted": true}], "parameters": {"groupId": "SNAPSHOT_TO_STEP_OPTIONS_OVERRIDE", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "more_content_telemetry", "payload": {"type": "json", "value": "{\n  \"CORTEX_TRAJECTORY_TYPE_USER_MAINLINE\": \"{\\\"code_step_creation_options\\\":{\\\"include_original_content\\\":true},\\\"view_file_step_creation_options\\\":{\\\"include_raw_content\\\":true}}\",\n  \"CORTEX_TRAJECTORY_TYPE_CASCADE\": \"{\\\"viewed_file_tracker_config\\\":{\\\"max_steps_per_checkpoint\\\":1}, \\\"step_type_allow_list\\\":[\\\"CORTEX_STEP_TYPE_VIEW_FILE\\\"]}\",\n  \"CORTEX_TRAJECTORY_TYPE_INTERACTIVE_CASCADE\": \"{}\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 800, "weightType": "", "stickiness": "default", "overrides": null}, {"name": "more_view_file_steps", "payload": {"type": "json", "value": "{\n  \"CORTEX_TRAJECTORY_TYPE_USER_MAINLINE\": \"{\\\"code_step_creation_options\\\":{\\\"include_original_content\\\":true},{\\\"viewed_file_tracker_config\\\":{\\\"max_steps_per_checkpoint\\\":3}, \\\"view_file_step_creation_options\\\":{\\\"include_raw_content\\\":true}}\",\n  \"CORTEX_TRAJECTORY_TYPE_CASCADE\": \"{\\\"viewed_file_tracker_config\\\":{\\\"max_steps_per_checkpoint\\\":1}, \\\"step_type_allow_list\\\":[\\\"CORTEX_STEP_TYPE_VIEW_FILE\\\"]}\",\n  \"CORTEX_TRAJECTORY_TYPE_INTERACTIVE_CASCADE\": \"{}\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 200, "weightType": "", "stickiness": "default", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-next"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "SNAPSHOT_TO_STEP_OPTIONS_OVERRIDE", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "more_content_telemetry", "payload": {"type": "json", "value": "{\n  \"CORTEX_TRAJECTORY_TYPE_USER_MAINLINE\": \"{\\\"code_step_creation_options\\\":{\\\"include_original_content\\\":true},\\\"view_file_step_creation_options\\\":{\\\"include_raw_content\\\":true}}\",\n  \"CORTEX_TRAJECTORY_TYPE_CASCADE\": \"{\\\"viewed_file_tracker_config\\\":{\\\"max_steps_per_checkpoint\\\":1}, \\\"step_type_allow_list\\\":[\\\"CORTEX_STEP_TYPE_VIEW_FILE\\\"]}\",\n  \"CORTEX_TRAJECTORY_TYPE_INTERACTIVE_CASCADE\": \"{}\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "SORT_EOM_FIRST": {"name": "SORT_EOM_FIRST", "type": "experiment", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "SORT_EOM_FIRST", "rollout": "100", "stickiness": "random"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "SPLIT_MODEL": {"name": "SPLIT_MODEL", "type": "release", "description": "", "enabled": false, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "SPLIT_MODEL", "rollout": "10", "stickiness": "random"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "STREAMING_COMPLETIONS": {"name": "STREAMING_COMPLETIONS", "type": "experiment", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "STREAMING_COMPLETIONS", "rollout": "100", "stickiness": "random"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "STREAMING_EXTERNAL_COMMAND": {"name": "STREAMING_EXTERNAL_COMMAND", "type": "experiment", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "extensionVersion", "operator": "SEMVER_LT", "values": [], "value": "1.22.4", "caseInsensitive": false, "inverted": true}], "parameters": {"groupId": "STREAMING_EXTERNAL_COMMAND", "rollout": "100", "stickiness": "random"}, "segments": null, "variants": []}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "STREAMING_EXTERNAL_COMMAND", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "STREAM_USER_SHELL_COMMANDS": {"name": "STREAM_USER_SHELL_COMMANDS", "type": "experiment", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "STREAM_USER_SHELL_COMMANDS", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "SUPERCOMPLETE_CODE_DIAGNOSTICS_TOP_K": {"name": "SUPERCOMPLETE_CODE_DIAGNOSTICS_TOP_K", "type": "experiment", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-insiders"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "SUPERCOMPLETE_CODE_DIAGNOSTICS_TOP_K", "rollout": "100", "stickiness": "random"}, "segments": null, "variants": [{"name": "k_is_5", "payload": {"type": "string", "value": "5"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "random", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf"], "value": "", "caseInsensitive": false, "inverted": false}, {"contextName": "ideVersion", "operator": "SEMVER_LT", "values": [], "value": "1.4.3", "caseInsensitive": false, "inverted": true}], "parameters": {"groupId": "SUPERCOMPLETE_CODE_DIAGNOSTICS_TOP_K", "rollout": "100", "stickiness": "random"}, "segments": null, "variants": [{"name": "k_is_5", "payload": {"type": "string", "value": "5"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "random", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-next"], "value": "", "caseInsensitive": false, "inverted": false}, {"contextName": "ideVersion", "operator": "SEMVER_LT", "values": [], "value": "1.5.107", "caseInsensitive": false, "inverted": true}], "parameters": {"groupId": "SUPERCOMPLETE_CODE_DIAGNOSTICS_TOP_K", "rollout": "100", "stickiness": "random"}, "segments": null, "variants": [{"name": "k_is_5", "payload": {"type": "string", "value": "5"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "random", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": true}, "SUPERCOMPLETE_DIAGNOSTIC_SEVERITY_THRESHOLD": {"name": "SUPERCOMPLETE_DIAGNOSTIC_SEVERITY_THRESHOLD", "type": "experiment", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-insiders"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "SUPERCOMPLETE_DIAGNOSTIC_SEVERITY_THRESHOLD", "rollout": "100", "stickiness": "random"}, "segments": null, "variants": [{"name": "all_diagnostics", "payload": {"type": "string", "value": "4"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "random", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["vscode"], "value": "", "caseInsensitive": false, "inverted": false}, {"contextName": "extensionVersion", "operator": "SEMVER_LT", "values": [], "value": "1.31.20", "caseInsensitive": false, "inverted": true}], "parameters": {"groupId": "SUPERCOMPLETE_DIAGNOSTIC_SEVERITY_THRESHOLD", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "all_diagnostics", "payload": {"type": "number", "value": "4"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf"], "value": "", "caseInsensitive": false, "inverted": false}, {"contextName": "ideVersion", "operator": "SEMVER_LT", "values": [], "value": "1.4.3", "caseInsensitive": false, "inverted": true}], "parameters": {"groupId": "SUPERCOMPLETE_DIAGNOSTIC_SEVERITY_THRESHOLD", "rollout": "100", "stickiness": "random"}, "segments": null, "variants": [{"name": "all_diagnostics", "payload": {"type": "number", "value": "4"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "random", "overrides": null}, {"name": "error_only", "payload": {"type": "number", "value": "0"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "random", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-next"], "value": "", "caseInsensitive": false, "inverted": false}, {"contextName": "ideVersion", "operator": "SEMVER_LT", "values": [], "value": "1.5.107", "caseInsensitive": false, "inverted": true}], "parameters": {"groupId": "SUPERCOMPLETE_DIAGNOSTIC_SEVERITY_THRESHOLD", "rollout": "100", "stickiness": "random"}, "segments": null, "variants": [{"name": "all_diagnostics", "payload": {"type": "number", "value": "4"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "random", "overrides": null}, {"name": "error_only", "payload": {"type": "number", "value": "0"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "random", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "SUPERCOMPLETE_DISABLE_TYPING_CACHE": {"name": "SUPERCOMPLETE_DISABLE_TYPING_CACHE", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-insiders"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "SUPERCOMPLETE_DISABLE_TYPING_CACHE", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-next"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "SUPERCOMPLETE_DISABLE_TYPING_CACHE", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf"], "value": "", "caseInsensitive": false, "inverted": false}, {"contextName": "extensionVersion", "operator": "SEMVER_GT", "values": [], "value": "1.32.1", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "SUPERCOMPLETE_DISABLE_TYPING_CACHE", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "SUPERCOMPLETE_DONT_FILTER_MID_STREAMED": {"name": "SUPERCOMPLETE_DONT_FILTER_MID_STREAMED", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "SUPERCOMPLETE_DONT_FILTER_MID_STREAMED", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "SUPERCOMPLETE_FAST_DEBOUNCE": {"name": "SUPERCOMPLETE_FAST_DEBOUNCE", "type": "release", "description": "Extension side debounce for supercomplete", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-insiders"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "SUPERCOMPLETE_FAST_DEBOUNCE", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "none", "payload": {"type": "number", "value": "0"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-next", "windsurf"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "SUPERCOMPLETE_FAST_DEBOUNCE", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "verysmall", "payload": {"type": "string", "value": "20"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "SUPERCOMPLETE_FILTER_DELETION_CAP": {"name": "SUPERCOMPLETE_FILTER_DELETION_CAP", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "SUPERCOMPLETE_FILTER_DELETION_CAP", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "SUPERCOMPLETE_FILTER_INSERTION_CAP": {"name": "SUPERCOMPLETE_FILTER_INSERTION_CAP", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "SUPERCOMPLETE_FILTER_INSERTION_CAP", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "SUPERCOMPLETE_FILTER_NO_OP": {"name": "SUPERCOMPLETE_FILTER_NO_OP", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "SUPERCOMPLETE_FILTER_NO_OP", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "SUPERCOMPLETE_FILTER_PREFIX_MATCH": {"name": "SUPERCOMPLETE_FILTER_PREFIX_MATCH", "type": "release", "description": "", "enabled": false, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "SUPERCOMPLETE_FILTER_PREFIX_MATCH", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "SUPERCOMPLETE_FILTER_PREVIOUSLY_SHOWN": {"name": "SUPERCOMPLETE_FILTER_PREVIOUSLY_SHOWN", "type": "release", "description": "", "enabled": false, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "SUPERCOMPLETE_FILTER_PREVIOUSLY_SHOWN", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "SUPERCOMPLETE_FILTER_REVERT": {"name": "SUPERCOMPLETE_FILTER_REVERT", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "SUPERCOMPLETE_FILTER_REVERT", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "SUPERCOMPLETE_FILTER_REVERT_AUTOCOMPLETE": {"name": "SUPERCOMPLETE_FILTER_REVERT_AUTOCOMPLETE", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf", "windsurf-next", "windsurf-insiders"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "SUPERCOMPLETE_FILTER_REVERT_AUTOCOMPLETE", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "SUPERCOMPLETE_FILTER_SCORE_THRESHOLD": {"name": "SUPERCOMPLETE_FILTER_SCORE_THRESHOLD", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "SUPERCOMPLETE_FILTER_SCORE_THRESHOLD", "rollout": "0", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "SUPERCOMPLETE_FILTER_WHITESPACE_ONLY": {"name": "SUPERCOMPLETE_FILTER_WHITESPACE_ONLY", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "extensionVersion", "operator": "SEMVER_LT", "values": [], "value": "1.30.0", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "SUPERCOMPLETE_FILTER_WHITESPACE_ONLY", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "SUPERCOMPLETE_INLINE_PURE_DELETE": {"name": "SUPERCOMPLETE_INLINE_PURE_DELETE", "type": "experiment", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "devMode", "operator": "IN", "values": ["true"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "SUPERCOMPLETE_INLINE_PURE_DELETE", "rollout": "100", "stickiness": "userId"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "SUPERCOMPLETE_INLINE_RICH_GHOST_TEXT_INSERTIONS": {"name": "SUPERCOMPLETE_INLINE_RICH_GHOST_TEXT_INSERTIONS", "type": "experiment", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "SUPERCOMPLETE_INLINE_RICH_GHOST_TEXT_INSERTIONS", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "SUPERCOMPLETE_LINE_RADIUS": {"name": "SUPERCOMPLETE_LINE_RADIUS", "type": "experiment", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-insiders"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "SUPERCOMPLETE_LINE_RADIUS", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "small_for_tab_jump", "payload": {"type": "number", "value": "5"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "SUPERCOMPLETE_LINE_RADIUS", "rollout": "100", "stickiness": "userId"}, "segments": null, "variants": [{"name": "large", "payload": {"type": "number", "value": "7"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "userId", "overrides": null}, {"name": "medium", "payload": {"type": "number", "value": "5"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "userId", "overrides": null}, {"name": "small", "payload": {"type": "number", "value": "3"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "userId", "overrides": null}, {"name": "xl", "payload": {"type": "number", "value": "15"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "userId", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "SUPERCOMPLETE_MAX_CONCURRENT_REQUESTS": {"name": "SUPERCOMPLETE_MAX_CONCURRENT_REQUESTS", "type": "release", "description": "", "enabled": false, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-insiders"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "SUPERCOMPLETE_MAX_CONCURRENT_REQUESTS", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "4", "payload": {"type": "number", "value": "1"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "SUPERCOMPLETE_MAX_DELETIONS": {"name": "SUPERCOMPLETE_MAX_DELETIONS", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "SUPERCOMPLETE_MAX_DELETIONS", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "md", "payload": {"type": "number", "value": "10"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}, {"name": "xl", "payload": {"type": "number", "value": "30"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "default", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "SUPERCOMPLETE_MAX_INSERTIONS": {"name": "SUPERCOMPLETE_MAX_INSERTIONS", "type": "experiment", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "SUPERCOMPLETE_MAX_INSERTIONS", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "large", "payload": {"type": "number", "value": "10"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "default", "overrides": null}, {"name": "medium", "payload": {"type": "number", "value": "5"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "default", "overrides": null}, {"name": "small", "payload": {"type": "number", "value": "3"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "default", "overrides": null}, {"name": "xlarge", "payload": {"type": "number", "value": "30"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "SUPERCOMPLETE_MAX_TRAJECTORY_STEPS": {"name": "SUPERCOMPLETE_MAX_TRAJECTORY_STEPS", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "extensionVersion", "operator": "SEMVER_LT", "values": [], "value": "1.30.0", "caseInsensitive": false, "inverted": true}], "parameters": {"groupId": "SUPERCOMPLETE_MAX_TRAJECTORY_STEPS", "rollout": "100", "stickiness": "userId"}, "segments": null, "variants": [{"name": "10_step", "payload": {"type": "number", "value": "10"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "userId", "overrides": null}, {"name": "15_step", "payload": {"type": "number", "value": "15"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "userId", "overrides": null}, {"name": "7_steps", "payload": {"type": "number", "value": "7"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "userId", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "SUPERCOMPLETE_MAX_TRAJECTORY_STEPS", "rollout": "100", "stickiness": "userId"}, "segments": null, "variants": [{"name": "extra-large", "payload": {"type": "number", "value": "10"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "userId", "overrides": null}, {"name": "large", "payload": {"type": "number", "value": "7"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "userId", "overrides": null}, {"name": "medium", "payload": {"type": "number", "value": "5"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "userId", "overrides": null}, {"name": "small", "payload": {"type": "number", "value": "2"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "userId", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "SUPERCOMPLETE_MAX_TRAJECTORY_STEP_SIZE": {"name": "SUPERCOMPLETE_MAX_TRAJECTORY_STEP_SIZE", "type": "experiment", "description": "", "enabled": false, "strategies": [], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": true}, "SUPERCOMPLETE_MIN_SCORE": {"name": "SUPERCOMPLETE_MIN_SCORE", "type": "experiment", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "SUPERCOMPLETE_MIN_SCORE", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "heavy", "payload": {"type": "number", "value": "-1"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "default", "overrides": null}, {"name": "light", "payload": {"type": "number", "value": "-3"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}, {"name": "medium", "payload": {"type": "number", "value": "-2"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "default", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "SUPERCOMPLETE_MODEL_CONFIG": {"name": "SUPERCOMPLETE_MODEL_CONFIG", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-insiders"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "SUPERCOMPLETE_MODEL_CONFIG", "rollout": "100", "stickiness": "sessionId"}, "segments": null, "variants": [{"name": "MODEL_CHAT_19820", "payload": {"type": "json", "value": "{\n  \"model_name\": \"MODEL_CHAT_19820\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "sessionId", "overrides": null}, {"name": "MODEL_CHAT_23310", "payload": {"type": "json", "value": "{\n  \"model_name\": \"MODEL_CHAT_23310\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "sessionId", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-next"], "value": "", "caseInsensitive": false, "inverted": false}, {"contextName": "ideVersion", "operator": "SEMVER_LT", "values": [], "value": "1.7.101", "caseInsensitive": false, "inverted": true}], "parameters": {"groupId": "SUPERCOMPLETE_MODEL_CONFIG", "rollout": "100", "stickiness": "sessionId"}, "segments": null, "variants": [{"name": "MODEL_CHAT_15305", "payload": {"type": "json", "value": "{\n  \"model_name\": \"MODEL_CHAT_15305\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "sessionId", "overrides": null}, {"name": "MODEL_CHAT_15729", "payload": {"type": "json", "value": "{\n  \"model_name\": \"MODEL_CHAT_15729\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "sessionId", "overrides": null}, {"name": "MODEL_CHAT_19820", "payload": {"type": "json", "value": "{\n  \"model_name\": \"MODEL_CHAT_19820\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "sessionId", "overrides": null}, {"name": "MODEL_CHAT_19821", "payload": {"type": "json", "value": "{\n  \"model_name\": \"MODEL_CHAT_19821\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "sessionId", "overrides": null}, {"name": "MODEL_CHAT_19822", "payload": {"type": "json", "value": "{\n  \"model_name\": \"MODEL_CHAT_19822\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "sessionId", "overrides": null}, {"name": "MODEL_CHAT_23310", "payload": {"type": "json", "value": "{\n  \"model_name\": \"MODEL_CHAT_23310\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "sessionId", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-next"], "value": "", "caseInsensitive": false, "inverted": false}, {"contextName": "ideVersion", "operator": "SEMVER_LT", "values": [], "value": "1.5.107", "caseInsensitive": false, "inverted": true}], "parameters": {"groupId": "SUPERCOMPLETE_MODEL_CONFIG", "rollout": "100", "stickiness": "sessionId"}, "segments": null, "variants": [{"name": "MODEL_CHAT_15729", "payload": {"type": "json", "value": "{\n  \"model_name\": \"MODEL_CHAT_15729\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "sessionId", "overrides": null}, {"name": "MODEL_CHAT_19821", "payload": {"type": "json", "value": "{\n  \"model_name\": \"MODEL_CHAT_19821\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "sessionId", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-next"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "SUPERCOMPLETE_MODEL_CONFIG", "rollout": "100", "stickiness": "random"}, "segments": null, "variants": [{"name": "MODEL_CHAT_15305", "payload": {"type": "json", "value": "{\n  \"model_name\": \"MODEL_CHAT_15305\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "random", "overrides": null}, {"name": "MODEL_CHAT_15729", "payload": {"type": "json", "value": "{\n  \"model_name\": \"MODEL_CHAT_15729\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "random", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf"], "value": "", "caseInsensitive": false, "inverted": false}, {"contextName": "ideVersion", "operator": "SEMVER_LT", "values": [], "value": "1.7.2", "caseInsensitive": false, "inverted": true}, {"contextName": "planName", "operator": "NOT_IN", "values": ["Enterprise", "Teams", "Teams Ultimate", "Hybrid"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "SUPERCOMPLETE_MODEL_CONFIG", "rollout": "100", "stickiness": "sessionId"}, "segments": null, "variants": [{"name": "MODEL_CHAT_19820", "payload": {"type": "json", "value": "{\n  \"model_name\": \"MODEL_CHAT_19820\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "sessionId", "overrides": null}, {"name": "MODEL_CHAT_19822", "payload": {"type": "json", "value": "{\n  \"model_name\": \"MODEL_CHAT_19822\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "sessionId", "overrides": null}, {"name": "MODEL_CHAT_23310", "payload": {"type": "json", "value": "{\n  \"model_name\": \"MODEL_CHAT_23310\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "sessionId", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf"], "value": "", "caseInsensitive": false, "inverted": false}, {"contextName": "ideVersion", "operator": "SEMVER_LT", "values": [], "value": "1.7.0", "caseInsensitive": false, "inverted": true}, {"contextName": "planName", "operator": "NOT_IN", "values": ["Enterprise", "Teams", "Teams Ultimate", "Hybrid"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "SUPERCOMPLETE_MODEL_CONFIG", "rollout": "100", "stickiness": "sessionId"}, "segments": null, "variants": [{"name": "MODEL_CHAT_19821", "payload": {"type": "json", "value": "{\n  \"model_name\": \"MODEL_CHAT_19821\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "sessionId", "overrides": null}, {"name": "MODEL_CHAT_19822", "payload": {"type": "json", "value": "{\n  \"model_name\": \"MODEL_CHAT_19822\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "sessionId", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf"], "value": "", "caseInsensitive": false, "inverted": false}, {"contextName": "ideVersion", "operator": "SEMVER_LT", "values": [], "value": "1.4.3", "caseInsensitive": false, "inverted": true}], "parameters": {"groupId": "SUPERCOMPLETE_MODEL_CONFIG", "rollout": "100", "stickiness": "sessionId"}, "segments": null, "variants": [{"name": "MODEL_CHAT_15729", "payload": {"type": "json", "value": "{\n  \"model_name\": \"MODEL_CHAT_15729\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "sessionId", "overrides": null}, {"name": "MODEL_CHAT_19821", "payload": {"type": "json", "value": "{\n  \"model_name\": \"MODEL_CHAT_19821\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "sessionId", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf"], "value": "", "caseInsensitive": false, "inverted": false}, {"contextName": "extensionVersion", "operator": "SEMVER_LT", "values": [], "value": "1.32.2", "caseInsensitive": false, "inverted": true}], "parameters": {"groupId": "SUPERCOMPLETE_MODEL_CONFIG", "rollout": "100", "stickiness": "random"}, "segments": null, "variants": [{"name": "MODEL_CHAT_15305", "payload": {"type": "json", "value": "{\n  \"model_name\": \"MODEL_CHAT_15305\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "random", "overrides": null}, {"name": "MODEL_CHAT_15729", "payload": {"type": "json", "value": "{\n  \"model_name\": \"MODEL_CHAT_15729\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "random", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf", "windsurf-insiders", "windsurf-next"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "SUPERCOMPLETE_MODEL_CONFIG", "rollout": "100", "stickiness": "random"}, "segments": null, "variants": [{"name": "MODEL_CHAT_15305", "payload": {"type": "json", "value": "{\n  \"model_name\": \"MODEL_CHAT_15305\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "random", "overrides": null}, {"name": "MODEL_CHAT_15729", "payload": {"type": "json", "value": "{\n  \"model_name\": \"MODEL_CHAT_15729\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "random", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "extensionVersion", "operator": "SEMVER_LT", "values": [], "value": "1.35.2", "caseInsensitive": false, "inverted": true}, {"contextName": "ide", "operator": "IN", "values": ["vscode"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "SUPERCOMPLETE_MODEL_CONFIG", "rollout": "100", "stickiness": "random"}, "segments": null, "variants": [{"name": "MODEL_CHAT_15305", "payload": {"type": "json", "value": "{\n  \"model_name\": \"MODEL_CHAT_15305\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "random", "overrides": null}, {"name": "MODEL_CHAT_15600", "payload": {"type": "json", "value": "{\n  \"model_name\": \"MODEL_CHAT_15600\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "random", "overrides": null}, {"name": "MODEL_CHAT_15729", "payload": {"type": "json", "value": "{\n  \"model_name\": \"MODEL_CHAT_15729\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "random", "overrides": null}, {"name": "MODEL_CHAT_16801", "payload": {"type": "json", "value": "{\n  \"model_name\": \"MODEL_CHAT_16801\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "random", "overrides": null}, {"name": "MODEL_CHAT_18805", "payload": {"type": "json", "value": "{\n  \"model_name\": \"MODEL_CHAT_18805\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "random", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "extensionVersion", "operator": "SEMVER_LT", "values": [], "value": "1.29.2", "caseInsensitive": false, "inverted": true}, {"contextName": "ide", "operator": "IN", "values": ["vscode"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "SUPERCOMPLETE_MODEL_CONFIG", "rollout": "100", "stickiness": "random"}, "segments": null, "variants": [{"name": "MODEL_CHAT_15305", "payload": {"type": "json", "value": "{\n  \"model_name\": \"MODEL_CHAT_15305\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "random", "overrides": null}, {"name": "MODEL_CHAT_15600", "payload": {"type": "json", "value": "{\n  \"model_name\": \"MODEL_CHAT_15600\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "random", "overrides": null}, {"name": "MODEL_CHAT_16801", "payload": {"type": "json", "value": "{\n  \"model_name\": \"MODEL_CHAT_16801\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "random", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "extensionVersion", "operator": "SEMVER_LT", "values": [], "value": "1.25.6", "caseInsensitive": false, "inverted": true}, {"contextName": "ide", "operator": "IN", "values": ["vscode"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "SUPERCOMPLETE_MODEL_CONFIG", "rollout": "100", "stickiness": "random"}, "segments": null, "variants": [{"name": "MODEL_CHAT_15305", "payload": {"type": "json", "value": "{\n  \"model_name\": \"MODEL_CHAT_15305\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "random", "overrides": null}, {"name": "MODEL_CHAT_15600", "payload": {"type": "json", "value": "{\n  \"model_name\": \"MODEL_CHAT_15600\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "random", "overrides": null}, {"name": "MODEL_CHAT_15729", "payload": {"type": "json", "value": "{\n  \"model_name\": \"MODEL_CHAT_15729\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "random", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "extensionVersion", "operator": "SEMVER_LT", "values": [], "value": "1.25.1", "caseInsensitive": false, "inverted": true}, {"contextName": "ide", "operator": "IN", "values": ["vscode"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "SUPERCOMPLETE_MODEL_CONFIG", "rollout": "100", "stickiness": "random"}, "segments": null, "variants": [{"name": "MODEL_CHAT_15305", "payload": {"type": "json", "value": "{\n  \"model_name\": \"MODEL_CHAT_15305\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "random", "overrides": null}, {"name": "MODEL_CHAT_15600", "payload": {"type": "json", "value": "{\n  \"model_name\": \"MODEL_CHAT_15600\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "random", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "extensionVersion", "operator": "SEMVER_LT", "values": [], "value": "1.21.8", "caseInsensitive": false, "inverted": false}, {"contextName": "ide", "operator": "IN", "values": ["vscode"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "SUPERCOMPLETE_MODEL_CONFIG", "rollout": "100", "stickiness": "random"}, "segments": null, "variants": [{"name": "MODEL_CHAT_13930", "payload": {"type": "json", "value": "{\n\"model_name\":\"MODEL_CHAT_13930\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "random", "overrides": null}, {"name": "MODEL_CHAT_14942", "payload": {"type": "json", "value": "{\n\"model_name\":\"MODEL_CHAT_14942\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "random", "overrides": null}, {"name": "MODEL_CHAT_15305", "payload": {"type": "json", "value": "{\n\"model_name\":\"MODEL_CHAT_15305\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "random", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["vscode"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "SUPERCOMPLETE_MODEL_CONFIG", "rollout": "100", "stickiness": "random"}, "segments": null, "variants": [{"name": "GPT_40_MINI", "payload": {"type": "json", "value": "{\n\"model_name\":\"MODEL_CHAT_GPT_4O_MINI_2024_07_18\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "random", "overrides": null}, {"name": "MODEL_CHAT_13930", "payload": {"type": "json", "value": "{\n\"model_name\":\"MODEL_CHAT_13930\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "random", "overrides": null}, {"name": "MODEL_CHAT_14942", "payload": {"type": "json", "value": "{\n\"model_name\":\"MODEL_CHAT_14942\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "random", "overrides": null}, {"name": "MODEL_CHAT_15305", "payload": {"type": "json", "value": "{\n\"model_name\":\"MODEL_CHAT_15305\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "random", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "SUPERCOMPLETE_NO_ACTIVE_NODE": {"name": "SUPERCOMPLETE_NO_ACTIVE_NODE", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "SUPERCOMPLETE_NO_ACTIVE_NODE", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "SUPERCOMPLETE_NO_CONTEXT": {"name": "SUPERCOMPLETE_NO_CONTEXT", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-insiders"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "SUPERCOMPLETE_NO_CONTEXT", "rollout": "100", "stickiness": "sessionId"}, "segments": null, "variants": []}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-next"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "SUPERCOMPLETE_NO_CONTEXT", "rollout": "50", "stickiness": "sessionId"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "SUPERCOMPLETE_ON_ACCEPT_ONLY": {"name": "SUPERCOMPLETE_ON_ACCEPT_ONLY", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "SUPERCOMPLETE_ON_ACCEPT_ONLY", "rollout": "10", "stickiness": "userId"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "SUPERCOMPLETE_ON_TAB": {"name": "SUPERCOMPLETE_ON_TAB", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "SUPERCOMPLETE_ON_TAB", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "SUPERCOMPLETE_POST_APPLY_DECORATION": {"name": "SUPERCOMPLETE_POST_APPLY_DECORATION", "type": "release", "description": "Highlight inserted text upon accepted completion", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "SUPERCOMPLETE_POST_APPLY_DECORATION", "rollout": "100", "stickiness": "userId"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "SUPERCOMPLETE_PRUNE_MAX_INSERT_DELETE_LINE_DELTA": {"name": "SUPERCOMPLETE_PRUNE_MAX_INSERT_DELETE_LINE_DELTA", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "SUPERCOMPLETE_PRUNE_MAX_INSERT_DELETE_LINE_DELTA", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "med", "payload": {"type": "number", "value": "3"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "default", "overrides": null}, {"name": "small", "payload": {"type": "number", "value": "1"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "SUPERCOMPLETE_PRUNE_RESPONSE": {"name": "SUPERCOMPLETE_PRUNE_RESPONSE", "type": "release", "description": "", "enabled": false, "strategies": [], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "SUPERCOMPLETE_RECENT_STEPS_DURATION": {"name": "SUPERCOMPLETE_RECENT_STEPS_DURATION", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "extensionVersion", "operator": "SEMVER_LT", "values": [], "value": "1.30.0", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "SUPERCOMPLETE_RECENT_STEPS_DURATION", "rollout": "100", "stickiness": "userId"}, "segments": null, "variants": [{"name": "extra-large", "payload": {"type": "number", "value": "100"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "userId", "overrides": null}, {"name": "large", "payload": {"type": "number", "value": "30"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "userId", "overrides": null}, {"name": "medium", "payload": {"type": "number", "value": "15"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "userId", "overrides": null}, {"name": "small", "payload": {"type": "number", "value": "10"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "userId", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "extensionVersion", "operator": "SEMVER_LT", "values": [], "value": "1.30.0", "caseInsensitive": false, "inverted": true}], "parameters": {"groupId": "SUPERCOMPLETE_RECENT_STEPS_DURATION", "rollout": "100", "stickiness": "userId"}, "segments": null, "variants": [{"name": "5min", "payload": {"type": "number", "value": "300"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "userId", "overrides": null}, {"name": "60min", "payload": {"type": "number", "value": "3600"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "userId", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "SUPERCOMPLETE_REGULAR_DEBOUNCE": {"name": "SUPERCOMPLETE_REGULAR_DEBOUNCE", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "SUPERCOMPLETE_REGULAR_DEBOUNCE", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "slow", "payload": {"type": "string", "value": "200"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "SUPERCOMPLETE_TEMPERATURE": {"name": "SUPERCOMPLETE_TEMPERATURE", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "SUPERCOMPLETE_TEMPERATURE", "rollout": "100", "stickiness": "random"}, "segments": null, "variants": [{"name": "TEMP_001", "payload": {"type": "number", "value": "0.01"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "random", "overrides": null}, {"name": "TEMP_01", "payload": {"type": "number", "value": "0.1"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "random", "overrides": null}, {"name": "TEMP_04", "payload": {"type": "number", "value": "0.4"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "random", "overrides": null}, {"name": "TEMP_07", "payload": {"type": "number", "value": "0.7"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "random", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "SUPERCOMPLETE_USE_CODE_DIAGNOSTICS": {"name": "SUPERCOMPLETE_USE_CODE_DIAGNOSTICS", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-insiders"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "SUPERCOMPLETE_USE_CODE_DIAGNOSTICS", "rollout": "100", "stickiness": "random"}, "segments": null, "variants": []}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf"], "value": "", "caseInsensitive": false, "inverted": false}, {"contextName": "ideVersion", "operator": "SEMVER_LT", "values": [], "value": "1.4.3", "caseInsensitive": false, "inverted": true}], "parameters": {"groupId": "SUPERCOMPLETE_USE_CODE_DIAGNOSTICS", "rollout": "100", "stickiness": "random"}, "segments": null, "variants": []}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-next"], "value": "", "caseInsensitive": false, "inverted": false}, {"contextName": "ideVersion", "operator": "SEMVER_LT", "values": [], "value": "1.5.107", "caseInsensitive": false, "inverted": true}], "parameters": {"groupId": "SUPERCOMPLETE_USE_CODE_DIAGNOSTICS", "rollout": "100", "stickiness": "random"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "SUPERCOMPLETE_USE_INTELLISENSE": {"name": "SUPERCOMPLETE_USE_INTELLISENSE", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "SUPERCOMPLETE_USE_INTELLISENSE", "rollout": "100", "stickiness": "userId"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "TAB_JUMP_ACCEPT_ENABLED": {"name": "TAB_JUMP_ACCEPT_ENABLED", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf"], "value": "", "caseInsensitive": false, "inverted": false}, {"contextName": "devMode", "operator": "IN", "values": ["true"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "TAB_JUMP_ACCEPT_ENABLED", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-insiders"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "TAB_JUMP_ACCEPT_ENABLED", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "prereleaseMode", "operator": "IN", "values": ["true"], "value": "", "caseInsensitive": false, "inverted": false}, {"contextName": "extensionVersion", "operator": "SEMVER_LT", "values": [], "value": "1.26.4", "caseInsensitive": false, "inverted": true}], "parameters": {"groupId": "TAB_JUMP_ACCEPT_ENABLED", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "devMode", "operator": "IN", "values": ["true"], "value": "", "caseInsensitive": false, "inverted": false}, {"contextName": "extensionVersion", "operator": "SEMVER_LT", "values": [], "value": "1.23.6", "caseInsensitive": false, "inverted": true}], "parameters": {"groupId": "TAB_JUMP_ACCEPT_ENABLED", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-next"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "TAB_JUMP_ACCEPT_ENABLED", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "TAB_JUMP_AS_CACHED_SUPERCOMPLETE": {"name": "TAB_JUMP_AS_CACHED_SUPERCOMPLETE", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-next"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "TAB_JUMP_AS_CACHED_SUPERCOMPLETE", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-insiders"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "TAB_JUMP_AS_CACHED_SUPERCOMPLETE", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "TAB_JUMP_CUMULATIVE_PROMPT_CONFIG": {"name": "TAB_JUMP_CUMULATIVE_PROMPT_CONFIG", "type": "experiment", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "TAB_JUMP_CUMULATIVE_PROMPT_CONFIG", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "8k_default", "payload": {"type": "json", "value": "{\"persistent_context_multiplier\": 0.25, \"persistent_active_document_multiplier\": 0.45, \"persistent_open_docs_multiplier\": 0.25, \"persistent_max_tokens_per_open_doc\": 2048, \"persistent_max_ccis_considered\": 25, \"trajectory_context_multiplier\": 0.5, \"trajectory_refresh_threshold_multiplier\": 0.9, \"trajectory_truncation_multiplier\": 0.5, \"ephemeral_context_multiplier\": 0.25, \"intent_reservation_tokens\": 512, \"ephemeral_active_document_multiplier\": 1.0, \"ephemeral_max_ccis_considered\": 0}"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": true}, "TAB_JUMP_ENABLED": {"name": "TAB_JUMP_ENABLED", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf"], "value": "", "caseInsensitive": false, "inverted": false}, {"contextName": "extensionVersion", "operator": "SEMVER_GT", "values": [], "value": "1.32.1", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "TAB_JUMP_ENABLED", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf"], "value": "", "caseInsensitive": false, "inverted": false}, {"contextName": "devMode", "operator": "IN", "values": ["true"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "TAB_JUMP_ENABLED", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-insiders"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "TAB_JUMP_ENABLED", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "prereleaseMode", "operator": "IN", "values": ["true"], "value": "", "caseInsensitive": false, "inverted": false}, {"contextName": "extensionVersion", "operator": "SEMVER_LT", "values": [], "value": "1.26.4", "caseInsensitive": false, "inverted": true}], "parameters": {"groupId": "TAB_JUMP_ENABLED", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "devMode", "operator": "IN", "values": ["true"], "value": "", "caseInsensitive": false, "inverted": false}, {"contextName": "extensionVersion", "operator": "SEMVER_LT", "values": [], "value": "1.23.6", "caseInsensitive": false, "inverted": true}], "parameters": {"groupId": "TAB_JUMP_ENABLED", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-next"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "TAB_JUMP_ENABLED", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "TAB_JUMP_FILTER_IN_SELECTION": {"name": "TAB_JUMP_FILTER_IN_SELECTION", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-insiders"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "TAB_JUMP_FILTER_IN_SELECTION", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "TAB_JUMP_LINE_RADIUS": {"name": "TAB_JUMP_LINE_RADIUS", "type": "experiment", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "TAB_JUMP_LINE_RADIUS", "rollout": "100", "stickiness": "userId"}, "segments": null, "variants": [{"name": "large", "payload": {"type": "number", "value": "60"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "userId", "overrides": null}, {"name": "medium", "payload": {"type": "number", "value": "40"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "userId", "overrides": null}, {"name": "medium-large", "payload": {"type": "number", "value": "50"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "userId", "overrides": null}, {"name": "small", "payload": {"type": "number", "value": "30"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "userId", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-next"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "TAB_JUMP_LINE_RADIUS", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "large", "payload": {"type": "number", "value": "60"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}, {"name": "small", "payload": {"type": "number", "value": "30"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "default", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-insiders"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "TAB_JUMP_LINE_RADIUS", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "large", "payload": {"type": "number", "value": "60"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}, {"name": "small", "payload": {"type": "number", "value": "30"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "default", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "TAB_JUMP_LINE_RADIUS", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "small", "payload": {"type": "number", "value": "30"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "TAB_JUMP_MIN_FILTER_RADIUS": {"name": "TAB_JUMP_MIN_FILTER_RADIUS", "type": "experiment", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "TAB_JUMP_MIN_FILTER_RADIUS", "rollout": "100", "stickiness": "userId"}, "segments": null, "variants": [{"name": "large", "payload": {"type": "number", "value": "7"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "userId", "overrides": null}, {"name": "medium", "payload": {"type": "number", "value": "5"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "userId", "overrides": null}, {"name": "small", "payload": {"type": "number", "value": "2"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "userId", "overrides": null}, {"name": "xl", "payload": {"type": "number", "value": "0"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "userId", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "TAB_JUMP_MODEL_CONFIG": {"name": "TAB_JUMP_MODEL_CONFIG", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf"], "value": "", "caseInsensitive": false, "inverted": false}, {"contextName": "ideVersion", "operator": "SEMVER_LT", "values": [], "value": "1.5.6", "caseInsensitive": false, "inverted": true}], "parameters": {"groupId": "TAB_JUMP_MODEL_CONFIG", "rollout": "100", "stickiness": "userId"}, "segments": null, "variants": [{"name": "model_chat_20706", "payload": {"type": "json", "value": "{\n  \"model_name\": \"MODEL_CHAT_20706\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "userId", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-next"], "value": "", "caseInsensitive": false, "inverted": false}, {"contextName": "ideVersion", "operator": "SEMVER_LT", "values": [], "value": "1.6.113", "caseInsensitive": false, "inverted": true}], "parameters": {"groupId": "TAB_JUMP_MODEL_CONFIG", "rollout": "100", "stickiness": "sessionId"}, "segments": null, "variants": [{"name": "MODEL_CHAT_20706", "payload": {"type": "json", "value": "{\n  \"model_name\": \"MODEL_CHAT_20706\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "sessionId", "overrides": null}, {"name": "MODEL_CHAT_21779", "payload": {"type": "json", "value": "{\n  \"model_name\": \"MODEL_CHAT_21779\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "sessionId", "overrides": null}, {"name": "MODEL_CHAT_22798", "payload": {"type": "json", "value": "{\n  \"model_name\": \"MODEL_CHAT_22798\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "sessionId", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-next"], "value": "", "caseInsensitive": false, "inverted": false}, {"contextName": "ideVersion", "operator": "SEMVER_LT", "values": [], "value": "1.5.107", "caseInsensitive": false, "inverted": true}], "parameters": {"groupId": "TAB_JUMP_MODEL_CONFIG", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "MODEL_CHAT_19484", "payload": {"type": "json", "value": "{\n  \"model_name\": \"MODEL_CHAT_19484\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "default", "overrides": null}, {"name": "MODEL_CHAT_20706", "payload": {"type": "json", "value": "{\n  \"model_name\": \"MODEL_CHAT_20706\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-next"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "TAB_JUMP_MODEL_CONFIG", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "MODEL_CHAT_15305", "payload": {"type": "json", "value": "{\n  \"model_name\": \"MODEL_CHAT_15305\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "default", "overrides": null}, {"name": "MODEL_CHAT_18468", "payload": {"type": "json", "value": "{\n  \"model_name\": \"MODEL_CHAT_18468\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-insiders"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "TAB_JUMP_MODEL_CONFIG", "rollout": "100", "stickiness": "sessionId"}, "segments": null, "variants": [{"name": "MODEL_CHAT_15305", "payload": {"type": "json", "value": "{\n  \"model_name\": \"MODEL_CHAT_15305\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "sessionId", "overrides": null}, {"name": "MODEL_CHAT_18468", "payload": {"type": "json", "value": "{\n  \"model_name\": \"MODEL_CHAT_18468\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "sessionId", "overrides": null}, {"name": "MODEL_CHAT_19484", "payload": {"type": "json", "value": "{\n  \"model_name\": \"MODEL_CHAT_19484\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "sessionId", "overrides": null}, {"name": "MODEL_CHAT_20706", "payload": {"type": "json", "value": "{\n  \"model_name\": \"MODEL_CHAT_20706\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "sessionId", "overrides": null}, {"name": "MODEL_CHAT_21779", "payload": {"type": "json", "value": "{\n  \"model_name\": \"MODEL_CHAT_21779\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "sessionId", "overrides": null}, {"name": "MODEL_CHAT_22798", "payload": {"type": "json", "value": "{\n  \"model_name\": \"MODEL_CHAT_22798\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "sessionId", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "TAB_JUMP_MODEL_CONFIG", "rollout": "100", "stickiness": "random"}, "segments": null, "variants": [{"name": "MODEL_CHAT_15305", "payload": {"type": "json", "value": "{\n  \"model_name\": \"MODEL_CHAT_15305\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "random", "overrides": null}, {"name": "MODEL_CHAT_18468", "payload": {"type": "json", "value": "{\n  \"model_name\": \"MODEL_CHAT_18468\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "random", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "extensionVersion", "operator": "SEMVER_LT", "values": [], "value": "1.33.2", "caseInsensitive": false, "inverted": true}], "parameters": {"groupId": "TAB_JUMP_MODEL_CONFIG", "rollout": "100", "stickiness": "random"}, "segments": null, "variants": [{"name": "MODEL_CHAT_15305", "payload": {"type": "json", "value": "{\n  \"model_name\": \"MODEL_CHAT_15305\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "random", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "extensionVersion", "operator": "SEMVER_LT", "values": [], "value": "1.29.2", "caseInsensitive": false, "inverted": true}], "parameters": {"groupId": "TAB_JUMP_MODEL_CONFIG", "rollout": "100", "stickiness": "random"}, "segments": null, "variants": [{"name": "MODEL_CHAT_15305", "payload": {"type": "json", "value": "{\n  \"model_name\": \"MODEL_CHAT_15305\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "random", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "extensionVersion", "operator": "SEMVER_LT", "values": [], "value": "1.25.6", "caseInsensitive": false, "inverted": true}], "parameters": {"groupId": "TAB_JUMP_MODEL_CONFIG", "rollout": "100", "stickiness": "random"}, "segments": null, "variants": [{"name": "MODEL_CHAT_15305", "payload": {"type": "json", "value": "{\n  \"model_name\": \"MODEL_CHAT_15305\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "random", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "extensionVersion", "operator": "SEMVER_LT", "values": [], "value": "1.25.1", "caseInsensitive": false, "inverted": true}], "parameters": {"groupId": "TAB_JUMP_MODEL_CONFIG", "rollout": "100", "stickiness": "random"}, "segments": null, "variants": [{"name": "MODEL_CHAT_15305", "payload": {"type": "json", "value": "{\n  \"model_name\": \"MODEL_CHAT_15305\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "random", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "TAB_JUMP_ON_ACCEPT_ONLY": {"name": "TAB_JUMP_ON_ACCEPT_ONLY", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf"], "value": "", "caseInsensitive": false, "inverted": false}, {"contextName": "extensionVersion", "operator": "SEMVER_LT", "values": [], "value": "1.40.2", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "TAB_JUMP_ON_ACCEPT_ONLY", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "TAB_JUMP_PRINT_LINE_RANGE": {"name": "TAB_JUMP_PRINT_LINE_RANGE", "type": "release", "description": "", "enabled": false, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-insiders"], "value": "", "caseInsensitive": false, "inverted": false}, {"contextName": "devMode", "operator": "IN", "values": ["true"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "TAB_JUMP_PRINT_LINE_RANGE", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-next"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "TAB_JUMP_PRINT_LINE_RANGE", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "TAB_JUMP_PRUNE_RESPONSE": {"name": "TAB_JUMP_PRUNE_RESPONSE", "type": "release", "description": "", "enabled": false, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf"], "value": "", "caseInsensitive": false, "inverted": false}, {"contextName": "extensionVersion", "operator": "SEMVER_GT", "values": [], "value": "1.32.1", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "TAB_JUMP_PRUNE_RESPONSE", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf"], "value": "", "caseInsensitive": false, "inverted": false}, {"contextName": "devMode", "operator": "IN", "values": ["true"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "TAB_JUMP_PRUNE_RESPONSE", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "TAB_JUMP_STOP_TOKEN_MIDSTREAM": {"name": "TAB_JUMP_STOP_TOKEN_MIDSTREAM", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf"], "value": "", "caseInsensitive": false, "inverted": false}, {"contextName": "ideVersion", "operator": "SEMVER_LT", "values": [], "value": "1.6.1", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "TAB_JUMP_STOP_TOKEN_MIDSTREAM", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-next"], "value": "", "caseInsensitive": false, "inverted": false}, {"contextName": "ideVersion", "operator": "SEMVER_LT", "values": [], "value": "1.6.115", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "TAB_JUMP_STOP_TOKEN_MIDSTREAM", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "TAB_REPORTING_KILL_SWITCH": {"name": "TAB_REPORTING_KILL_SWITCH", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "TAB_REPORTING_KILL_SWITCH", "rollout": "100", "stickiness": "userId"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "USE_ANTHROPIC_TOKEN_EFFICIENT_TOOLS_BETA": {"name": "USE_ANTHROPIC_TOKEN_EFFICIENT_TOOLS_BETA", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "USE_ANTHROPIC_TOKEN_EFFICIENT_TOOLS_BETA", "rollout": "10", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "USE_ATTRIBUTION_FOR_INDIVIDUAL_TIER": {"name": "USE_ATTRIBUTION_FOR_INDIVIDUAL_TIER", "type": "experiment", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "USE_ATTRIBUTION_FOR_INDIVIDUAL_TIER", "rollout": "100", "stickiness": "random"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "USE_AUTOCOMPLETE_MODEL": {"name": "USE_AUTOCOMPLETE_MODEL", "type": "operational", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "prereleaseMode", "operator": "IN", "values": ["true"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "USE_AUTOCOMPLETE_MODEL", "rollout": "100", "stickiness": "random"}, "segments": null, "variants": [{"name": "MODEL_14602", "payload": {"type": "json", "value": "{\"model_name\": \"MODEL_14602\"}"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "random", "overrides": null}, {"name": "MODEL_15133", "payload": {"type": "json", "value": "{\"model_name\": \"MODEL_15133\"}"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "random", "overrides": null}, {"name": "MODEL_8341", "payload": {"type": "json", "value": "{\"model_name\": \"MODEL_8341\"}"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "random", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "devMode", "operator": "IN", "values": ["true"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "USE_AUTOCOMPLETE_MODEL", "rollout": "100", "stickiness": "random"}, "segments": null, "variants": [{"name": "MODEL_14602", "payload": {"type": "json", "value": "{\"model_name\": \"MODEL_14602\"}"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "random", "overrides": null}, {"name": "MODEL_15133", "payload": {"type": "json", "value": "{\"model_name\": \"MODEL_15133\"}"}, "enabled": false, "featureEnabled": false, "weight": 500, "weightType": "", "stickiness": "random", "overrides": null}, {"name": "MODEL_8341", "payload": {"type": "json", "value": "{\"model_name\": \"MODEL_8341\"}"}, "enabled": false, "featureEnabled": false, "weight": 500, "weightType": "", "stickiness": "random", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "USE_AUTOCOMPLETE_MODEL", "rollout": "100", "stickiness": "random"}, "segments": null, "variants": [{"name": "MODEL_14602", "payload": {"type": "json", "value": "{\"model_name\": \"MODEL_14602\"}"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "random", "overrides": null}, {"name": "MODEL_8341", "payload": {"type": "json", "value": "{\"model_name\": \"MODEL_8341\"}"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "random", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [{"name": "MODEL_8341", "payload": {"type": "json", "value": "{\"model_name\":\"MODEL_8341\"}"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "variable", "stickiness": "random", "overrides": []}], "dependencies": null, "impressionData": true}, "USE_AUTOCOMPLETE_MODEL_SERVER_SIDE": {"name": "USE_AUTOCOMPLETE_MODEL_SERVER_SIDE", "type": "experiment", "description": "Autocomplete model used by server side model reselection.", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-next"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "USE_AUTOCOMPLETE_MODEL_SERVER_SIDE", "rollout": "100", "stickiness": "random"}, "segments": null, "variants": [{"name": "MODEL_CHAT_15729", "payload": {"type": "json", "value": "{\"model_name\": \"MODEL_CHAT_15729\"}"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "random", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf"], "value": "", "caseInsensitive": false, "inverted": false}, {"contextName": "extensionVersion", "operator": "SEMVER_LT", "values": [], "value": "1.32.2", "caseInsensitive": false, "inverted": true}], "parameters": {"groupId": "USE_AUTOCOMPLETE_MODEL_SERVER_SIDE", "rollout": "100", "stickiness": "random"}, "segments": null, "variants": [{"name": "MODEL_15133", "payload": {"type": "json", "value": "{\"model_name\": \"MODEL_15133\"}"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "random", "overrides": null}, {"name": "MODEL_CHAT_15729", "payload": {"type": "json", "value": "{\"model_name\": \"MODEL_CHAT_15729\"}"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "random", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf", "windsurf-next", "windsurf-insiders"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "USE_AUTOCOMPLETE_MODEL_SERVER_SIDE", "rollout": "100", "stickiness": "random"}, "segments": null, "variants": [{"name": "MODEL_15133", "payload": {"type": "json", "value": "{\"model_name\": \"MODEL_15133\"}"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "random", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "extensionVersion", "operator": "SEMVER_LT", "values": [], "value": "1.35.2", "caseInsensitive": false, "inverted": true}, {"contextName": "prereleaseMode", "operator": "IN", "values": ["true"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "USE_AUTOCOMPLETE_MODEL_SERVER_SIDE", "rollout": "100", "stickiness": "random"}, "segments": null, "variants": [{"name": "MODEL_15133", "payload": {"type": "json", "value": "{\"model_name\": \"MODEL_15133\"}"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "random", "overrides": null}, {"name": "MODEL_CHAT_15729", "payload": {"type": "json", "value": "{\"model_name\": \"MODEL_CHAT_15729\"}"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "random", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "USE_AUTOCOMPLETE_MODEL_SERVER_SIDE", "rollout": "100", "stickiness": "random"}, "segments": null, "variants": [{"name": "MODEL_15133", "payload": {"type": "json", "value": "{\"model_name\": \"MODEL_15133\"}"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "random", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": true}, "USE_CHAT_INSTRUCT_COMPLETION": {"name": "USE_CHAT_INSTRUCT_COMPLETION", "type": "experiment", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "teamsMode", "operator": "IN", "values": ["false"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "USE_CHAT_INSTRUCT_COMPLETION", "rollout": "50", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "USE_COMMAND_DOCSTRING_GENERATION": {"name": "USE_COMMAND_DOCSTRING_GENERATION", "type": "experiment", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "USE_COMMAND_DOCSTRING_GENERATION", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "USE_CUSTOM_CHARACTER_DIFF": {"name": "USE_CUSTOM_CHARACTER_DIFF", "type": "experiment", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-insiders"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "USE_CUSTOM_CHARACTER_DIFF", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "USE_CUSTOM_CHARACTER_DIFF", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-next"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "USE_CUSTOM_CHARACTER_DIFF", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "USE_GCP_API_SERVER_FOR_PREMIUM_CHAT": {"name": "USE_GCP_API_SERVER_FOR_PREMIUM_CHAT", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "USE_GCP_API_SERVER_FOR_PREMIUM_CHAT", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "USE_GPT_4_COMMAND": {"name": "USE_GPT_4_COMMAND", "type": "experiment", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "teamsMode", "operator": "IN", "values": ["false"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "USE_GPT_4_COMMAND", "rollout": "100", "stickiness": "userId"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "USE_GPT_4_TURBO": {"name": "USE_GPT_4_TURBO", "type": "release", "description": "Switches GPT4 to GPT4-turbo", "enabled": false, "strategies": [], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "USE_IMPLICIT_TRAJECTORY": {"name": "USE_IMPLICIT_TRAJECTORY", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "extensionVersion", "operator": "SEMVER_LT", "values": [], "value": "1.12.1", "caseInsensitive": false, "inverted": true}, {"contextName": "devMode", "operator": "IN", "values": ["true"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "USE_IMPLICIT_TRAJECTORY", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "extensionVersion", "operator": "SEMVER_LT", "values": [], "value": "1.14.15", "caseInsensitive": false, "inverted": true}], "parameters": {"groupId": "USE_IMPLICIT_TRAJECTORY", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "USE_IMPLICIT_TRAJECTORY", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "teamsMode", "operator": "IN", "values": ["true"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "USE_IMPLICIT_TRAJECTORY", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-next"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "USE_IMPLICIT_TRAJECTORY", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-insiders"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "USE_IMPLICIT_TRAJECTORY", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "USE_INFERENCE_API_SERVER": {"name": "USE_INFERENCE_API_SERVER", "type": "operational", "description": "PLEASE UPDATE https://exafunction.pagerduty.com/rules/rulesets/_default IF YOU TURN THIS OFF/ON", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "USE_INFERENCE_API_SERVER", "rollout": "100", "stickiness": "random"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "USE_MODEL_8341": {"name": "USE_MODEL_8341", "type": "experiment", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "USE_MODEL_8341", "rollout": "100", "stickiness": "random"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "USE_MODEL_8684": {"name": "USE_MODEL_8684", "type": "release", "description": "", "enabled": false, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "USE_MODEL_8684", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "USE_MQUERY_SCORER": {"name": "USE_MQUERY_SCORER", "type": "operational", "description": "", "enabled": false, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "extensionVersion", "operator": "SEMVER_GT", "values": [], "value": "1.8.53", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "USE_MQUERY_SCORER", "rollout": "15", "stickiness": "random"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "USE_MULTILINE_MODEL": {"name": "USE_MULTILINE_MODEL", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "USE_MULTILINE_MODEL", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "USE_OPENAI_INTERFACE_CLIENT": {"name": "USE_OPENAI_INTERFACE_CLIENT", "type": "release", "description": "Use openai v2 client", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "USE_OPENAI_INTERFACE_CLIENT", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "USE_OPENAI_OFFICIAL_CLIENT": {"name": "USE_OPENAI_OFFICIAL_CLIENT", "type": "experiment", "description": "Use openai_client_official.go rather than openai_client_v2 for all requests", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-insiders"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "USE_OPENAI_OFFICIAL_CLIENT", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-next"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "USE_OPENAI_OFFICIAL_CLIENT", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "USE_OPENAI_OFFICIAL_CLIENT", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}, {"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "USE_OPENAI_OFFICIAL_CLIENT", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "USE_QUANTIZED_FAISS_INDEX": {"name": "USE_QUANTIZED_FAISS_INDEX", "type": "experiment", "description": "Enables the quantized FAISS index for local indexing", "enabled": true, "strategies": [{"id": 0, "name": "default", "constraints": [], "parameters": {}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "USE_REMOTE_EMBEDDING": {"name": "USE_REMOTE_EMBEDDING", "type": "experiment", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "USE_REMOTE_EMBEDDING", "rollout": "100", "stickiness": "random"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "USE_SPECIAL_EDIT_CODE_BLOCK": {"name": "USE_SPECIAL_EDIT_CODE_BLOCK", "type": "experiment", "description": "Whether a chat model should use a special \"edit\" code block to signify edits that can be fast applied.", "enabled": false, "strategies": [], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": true}, "USE_SUPERCOMPLETE_MODEL": {"name": "USE_SUPERCOMPLETE_MODEL", "type": "experiment", "description": "", "enabled": false, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "extensionVersion", "operator": "SEMVER_GT", "values": [], "value": "1.8.12", "caseInsensitive": false, "inverted": true}], "parameters": {"groupId": "USE_SUPERCOMPLETE_MODEL", "rollout": "100", "stickiness": "random"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [{"name": "MODEL_CHAT_11121", "payload": {"type": "json", "value": "{\"model_name\":\"MODEL_CHAT_10546\"}"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "fix", "stickiness": "random", "overrides": []}, {"name": "MODEL_CHAT_3_5_TURBO", "payload": {"type": "json", "value": "{\"model_name\":\"MODEL_CHAT_3_5_TURBO\"}"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "fix", "stickiness": "random", "overrides": []}, {"name": "UNSPECIFIED", "payload": {"type": "json", "value": "{\"model_name\":\"MODEL_UNSPECIFIED\"}"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "variable", "stickiness": "random", "overrides": []}], "dependencies": null, "impressionData": false}, "VIEWED_FILE_TRACKER_CONFIG": {"name": "VIEWED_FILE_TRACKER_CONFIG", "type": "experiment", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-insiders"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "VIEWED_FILE_TRACKER_CONFIG", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "0_steps", "payload": {"type": "json", "value": "{\n  \"max_steps_per_checkpoint\": 0\n}"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "default", "overrides": null}, {"name": "2_steps", "payload": {"type": "json", "value": "{\n  \"max_steps_per_checkpoint\": 2\n}"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "VIEWED_FILE_TRACKER_CONFIG", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "0_steps", "payload": {"type": "json", "value": "{\n  \"max_steps_per_checkpoint\": 0\n}"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "default", "overrides": null}, {"name": "2_steps", "payload": {"type": "json", "value": "{\n  \"max_steps_per_checkpoint\": 2\n}"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-next"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "VIEWED_FILE_TRACKER_CONFIG", "rollout": "100", "stickiness": "random"}, "segments": null, "variants": [{"name": "2_steps", "payload": {"type": "json", "value": "{\n  \"max_steps_per_checkpoint\": 2\n}"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "random", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "extensionVersion", "operator": "SEMVER_LT", "values": [], "value": "1.40.2", "caseInsensitive": false, "inverted": true}, {"contextName": "ide", "operator": "IN", "values": ["vscode"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "VIEWED_FILE_TRACKER_CONFIG", "rollout": "100", "stickiness": "random"}, "segments": null, "variants": [{"name": "2_steps_0_included", "payload": {"type": "json", "value": "{   \n  \"max_steps_per_checkpoint\": 2, \n  \"max_files_in_prompt\": 0,   \n  \"max_lines_per_file_in_prompt\": 0\n}"}, "enabled": false, "featureEnabled": false, "weight": 475, "weightType": "", "stickiness": "random", "overrides": null}, {"name": "2_steps_2_included", "payload": {"type": "json", "value": "{   \n  \"max_steps_per_checkpoint\": 2, \n  \"max_files_in_prompt\": 2,   \n  \"max_lines_per_file_in_prompt\": 100 \n}"}, "enabled": false, "featureEnabled": false, "weight": 475, "weightType": "", "stickiness": "random", "overrides": null}, {"name": "2_steps_4_included", "payload": {"type": "json", "value": "{   \n  \"max_steps_per_checkpoint\": 2, \n  \"max_files_in_prompt\": 4,   \n  \"max_lines_per_file_in_prompt\": 100 \n}"}, "enabled": false, "featureEnabled": false, "weight": 50, "weightType": "", "stickiness": "random", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "VSCODE_USE_GRPC_PROTOCOL": {"name": "VSCODE_USE_GRPC_PROTOCOL", "type": "experiment", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "VSCODE_USE_GRPC_PROTOCOL", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "WAVE_8_KNOWLEDGE_ENABLED": {"name": "WAVE_8_KNOWLEDGE_ENABLED", "type": "release", "description": "This flag gates whether or not the Knowledge category is visible in the @ mention menu.", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "devMode", "operator": "IN", "values": ["true"], "value": "", "caseInsensitive": false, "inverted": false}, {"contextName": "ide", "operator": "IN", "values": ["jetbrains"], "value": "", "caseInsensitive": false, "inverted": true}], "parameters": {"groupId": "WAVE_8_KNOWLEDGE_ENABLED", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-insiders", "windsurf", "windsurf-next"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "WAVE_8_KNOWLEDGE_ENABLED", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "WAVE_8_RULES_ENABLED": {"name": "WAVE_8_RULES_ENABLED", "type": "release", "description": "This flag gates the backend for rules and whether or not the Rules category is visible in the @ mention menu. Everything is default off and is only enabled if the user has the dev extension or this flag is enabled.", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-insiders"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "WAVE_8_RULES_ENABLED", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-next", "windsurf", "windsurf-dev"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "WAVE_8_RULES_ENABLED", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "WINDSURF_SENTRY_SAMPLE_RATE": {"name": "WINDSURF_SENTRY_SAMPLE_RATE", "type": "release", "description": "Unleash flag to specify Sentry sample rate for Windsurf extension; Float in range [0.0, 1.0]", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "WINDSURF_SENTRY_SAMPLE_RATE", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "sample_rate", "payload": {"type": "number", "value": "0.01"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "XML_TOOL_PARSING_MODELS": {"name": "XML_TOOL_PARSING_MODELS", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ideVersion", "operator": "SEMVER_GT", "values": [], "value": "1.5.9", "caseInsensitive": false, "inverted": false}, {"contextName": "requestedModelId", "operator": "IN", "values": ["MODEL_CHAT_O3_MINI", "MODEL_GOOGLE_GEMINI_2_5_PRO"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "XML_TOOL_PARSING_MODELS", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "xmlmodels", "payload": {"type": "json", "value": "{\n  \"models\": [\n      \"MODEL_CHAT_O3_MINI\",\n      \"MODEL_GOOGLE_GEMINI_2_5_PRO\"\n  ]\n}"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "requestedModelId", "operator": "IN", "values": ["MODEL_XAI_GROK_3", "MODEL_XAI_GROK_3_MINI_REASONING"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "XML_TOOL_PARSING_MODELS", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "grok3", "payload": {"type": "json", "value": "{\n  \"models\": [\n    \"MODEL_XAI_GROK_3\",\n    \"MODEL_XAI_GROK_3_MINI_REASONING\"\n  ]\n}"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "add-session-id": {"name": "add-session-id", "type": "release", "description": "adds session id to gemini requests to", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "add-session-id", "rollout": "50", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "all-telemetry-kill-switch": {"name": "all-telemetry-kill-switch", "type": "release", "description": "", "enabled": false, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "all-telemetry-kill-switch", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "api-provider-routing-config": {"name": "api-provider-routing-config", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "api-provider-routing-config", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "default", "payload": {"type": "json", "value": "{\n  \"model_map\": {\n    \"MODEL_CLAUDE_4_SONNET\": {\n      \"provider_map\": {\n        \"API_PROVIDER_OPEN_ROUTER\": {\n          \"weight\": 1,\n          \"cache_ttl_minutes\": 5\n        },\n        \"API_PROVIDER_DATABRICKS\": {\n          \"weight\": 100,\n          \"cache_ttl_minutes\": 5\n        }\n      }\n    },\n    \"MODEL_CLAUDE_3_5_SONNET_20241022\": {\n      \"provider_map\": {\n        \"API_PROVIDER_ANTHROPIC_BEDROCK\": {\n          \"weight\": 1,\n          \"cache_ttl_minutes\": 5\n        }\n      }\n    },\n    \"MODEL_CLAUDE_3_7_SONNET_20250219\": {\n      \"provider_map\": {\n        \"API_PROVIDER_ANTHROPIC_BEDROCK\": {\n          \"weight\": 100,\n          \"cache_ttl_minutes\": 5\n        },\n        \"API_PROVIDER_DATABRICKS\": {\n          \"weight\": 70,\n          \"cache_ttl_minutes\": 5\n        }\n      }\n    },\n    \"MODEL_GOOGLE_GEMINI_2_5_PRO\": {\n      \"provider_map\": {\n        \"API_PROVIDER_GOOGLE_GEMINI\": {\n          \"weight\": 1,\n          \"cache_ttl_minutes\": 5\n        },\n        \"API_PROVIDER_GOOGLE_GENAI_VERTEX\": {\n          \"weight\": 10,\n          \"cache_ttl_minutes\": 5\n        }\n      }\n    }\n  }\n}"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-insiders"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "api-provider-routing-config", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "default", "payload": {"type": "json", "value": "{\n  \"model_map\": {\n    \"MODEL_CLAUDE_3_5_SONNET_20241022\": {\n      \"provider_map\": {\n        \"API_PROVIDER_ANTHROPIC_BEDROCK\": {\n          \"weight\": 1,\n          \"cache_ttl_minutes\": 5\n        }\n      }\n    },\n    \"MODEL_CLAUDE_3_7_SONNET_20250219\": {\n      \"provider_map\": {\n        \"API_PROVIDER_ANTHROPIC_BEDROCK\": {\n          \"weight\": 100,\n          \"cache_ttl_minutes\": 5\n        },\n        \"API_PROVIDER_DATABRICKS\": {\n          \"weight\": 0,\n          \"cache_ttl_minutes\": 0\n        }\n      }\n    },\n    \"MODEL_GOOGLE_GEMINI_2_5_PRO\": {\n      \"provider_map\": {\n        \"API_PROVIDER_GOOGLE_GEMINI\": {\n          \"weight\": 1,\n          \"cache_ttl_minutes\": 5\n        },\n        \"API_PROVIDER_GOOGLE_GENAI_VERTEX\": {\n          \"weight\": 10,\n          \"cache_ttl_minutes\": 5\n        }\n      }\n    }\n  }\n}"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "api-to-session-migration": {"name": "api-to-session-migration", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "api-to-session-migration", "rollout": "50", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "autoRunIntervalExperimentMs": {"name": "autoRunIntervalExperimentMs", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "autoRunIntervalExperimentMs", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "autoRunIntervalExperimentMs", "payload": {"type": "number", "value": "2000"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "browser-interactions-num-implicit-steps": {"name": "browser-interactions-num-implicit-steps", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-insiders"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "browser-interactions-num-implicit-steps", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "allow-interactions", "payload": {"type": "number", "value": "10"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "cascade-add-annotation": {"name": "cascade-add-annotation", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "cascade-add-annotation", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "cascade-add-annotation-conversational-mixin": {"name": "cascade-add-annotation-conversational-mixin", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "cascade-add-annotation-conversational-mixin", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "cascade-additional-instructions-section-content": {"name": "cascade-additional-instructions-section-content", "type": "experiment", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-insiders"], "value": "", "caseInsensitive": false, "inverted": false}, {"contextName": "requestedModelId", "operator": "IN", "values": ["MODEL_PRIVATE_3", "MODEL_CHAT_GPT_4_1_2025_04_14"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "cascade-additional-instructions-section-content", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "gpt4.1", "payload": {"type": "json", "value": "{ \"mode\": \"SECTION_OVERRIDE_MODE_UNSPECIFIED\", \"content\": \"\" \n}"}, "enabled": false, "featureEnabled": false, "weight": 334, "weightType": "", "stickiness": "default", "overrides": null}, {"name": "gpt4.1_devin", "payload": {"type": "json", "value": "{ \"mode\": \"SECTION_OVERRIDE_MODE_OVERRIDE\", \"content\": \"Do not guess, generalize, or infer behavior from names, comments, or patterns. Every claim must be supported by code you have examined. Use your research tools to thoroughly investigate and directly inspect relevant code before responding. Prioritize accuracy and completeness over speed; take the time to fully understand the codebase before making recommendations or changes. Continue researching until you have complete, code-backed confidence, and clearly state if you cannot find direct evidence.\" \n}"}, "enabled": false, "featureEnabled": false, "weight": 333, "weightType": "", "stickiness": "default", "overrides": null}, {"name": "gpt4.1_julian", "payload": {"type": "json", "value": "{ \"mode\": \"SECTION_OVERRIDE_MODE_OVERRIDE\", \"content\": \"You are an agent - please keep going until the user's query is completely resolved, before ending your turn and yielding back to the user. Only terminate your turn when you are sure that the problem is solved. Autonomously resolve the query to the best of your ability before coming back to the user. \\n\\nIf you are not sure about file content or codebase structure pertaining to the user's request, use your tools to read files and gather the relevant information: do NOT guess or make up an answer. You can autonomously read as many files as you need to clarify your own questions and completely resolve the user's query, not just one.\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 333, "weightType": "", "stickiness": "default", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf"], "value": "", "caseInsensitive": false, "inverted": false}, {"contextName": "requestedModelId", "operator": "IN", "values": ["MODEL_PRIVATE_3", "MODEL_CHAT_GPT_4_1_2025_04_14"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "cascade-additional-instructions-section-content", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "gpt4.1", "payload": {"type": "json", "value": "{ \"mode\": \"SECTION_OVERRIDE_MODE_UNSPECIFIED\", \"content\": \"\" \n}"}, "enabled": false, "featureEnabled": false, "weight": 334, "weightType": "", "stickiness": "default", "overrides": null}, {"name": "gpt4.1_devin", "payload": {"type": "json", "value": "{ \"mode\": \"SECTION_OVERRIDE_MODE_OVERRIDE\", \"content\": \"Do not guess, generalize, or infer behavior from names, comments, or patterns. Every claim must be supported by code you have examined. Use your research tools to thoroughly investigate and directly inspect relevant code before responding. Prioritize accuracy and completeness over speed; take the time to fully understand the codebase before making recommendations or changes. Continue researching until you have complete, code-backed confidence, and clearly state if you cannot find direct evidence.\" \n}"}, "enabled": false, "featureEnabled": false, "weight": 333, "weightType": "", "stickiness": "default", "overrides": null}, {"name": "gpt4.1_julian", "payload": {"type": "json", "value": "{ \"mode\": \"SECTION_OVERRIDE_MODE_OVERRIDE\", \"content\": \"You are an agent - please keep going until the user's query is completely resolved, before ending your turn and yielding back to the user. Only terminate your turn when you are sure that the problem is solved. Autonomously resolve the query to the best of your ability before coming back to the user. \\n\\nIf you are not sure about file content or codebase structure pertaining to the user's request, use your tools to read files and gather the relevant information: do NOT guess or make up an answer. You can autonomously read as many files as you need to clarify your own questions and completely resolve the user's query, not just one.\" }"}, "enabled": false, "featureEnabled": false, "weight": 333, "weightType": "", "stickiness": "default", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "requestedModelId", "operator": "IN", "values": ["MODEL_GOOGLE_GEMINI_2_5_PRO"], "value": "", "caseInsensitive": false, "inverted": false}, {"contextName": "ide", "operator": "IN", "values": ["windsurf-next", "windsurf", "windsurf-insiders"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "cascade-additional-instructions-section-content", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "default", "payload": {"type": "json", "value": "{ \"mode\": \"SECTION_OVERRIDE_MODE_UNSPECIFIED\", \"content\": \"\" \n}"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "default", "overrides": null}, {"name": "discourage-leakage", "payload": {"type": "json", "value": "{ \"mode\": \"SECTION_OVERRIDE_MODE_OVERRIDE\", \"content\": \"You are an agent - please keep going until the user's query is completely resolved, before ending your turn and yielding back to the user. Only terminate your turn when you are sure that the problem is solved.\\nIf you are not sure about file content or codebase structure pertaining to the user's request, use your tools to read files and gather the relevant information: do NOT guess or make up an answer.\\nYou MUST plan extensively before each function call, and reflect extensively on the outcomes of the previous function calls. DO NOT do this entire process by making function calls only, as this can impair your ability to solve the problem and think insightfully.\\nIMPORTANT: When calling tools, it is CRITICAL that you follow the EXACT XML format. The text between the XML brackets should be the EXACT tool name. Make sure you know the exact tool name, and do not add any prefix.\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 500, "weightType": "", "stickiness": "default", "overrides": null}, {"name": "gemini-2.5-pro-preview-06-05", "payload": {"type": "json", "value": "{ \"mode\": \"SECTION_OVERRIDE_MODE_OVERRIDE\", \"content\": \"You are an agent - please keep going until the user's query is completely resolved, before ending your turn and yielding back to the user. Only terminate your turn when you are sure that the problem is solved.\\nIf you are not sure about file content or codebase structure pertaining to the user's request, use your tools to read files and gather the relevant information: do NOT guess or make up an answer.\\nAct as a true pair-programming partner. Take initiative on clearly defined tasks, but if the user's intent is ambiguous, follow the user's lead and avoid jumping to conclusions.\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "default", "overrides": null}, {"name": "gpt4.1", "payload": {"type": "json", "value": "{ \"mode\": \"SECTION_OVERRIDE_MODE_OVERRIDE\", \"content\": \"You are an agent - please keep going until the user's query is completely resolved, before ending your turn and yielding back to the user. Only terminate your turn when you are sure that the problem is solved.\\nIf you are not sure about file content or codebase structure pertaining to the user's request, use your tools to read files and gather the relevant information: do NOT guess or make up an answer.\\nYou MUST plan extensively before each function call, and reflect extensively on the outcomes of the previous function calls. DO NOT do this entire process by making function calls only, as this can impair your ability to solve the problem and think insightfully.\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 500, "weightType": "", "stickiness": "default", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "cascade-api-server-experiment-keys": {"name": "cascade-api-server-experiment-keys", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "cascade-api-server-experiment-keys", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "all", "payload": {"type": "string", "value": "XML_TOOL_PARSING_MODELS,gemini-xml-tool-fixes,use-responses-api,add-session-id"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "cascade-brain-config": {"name": "cascade-brain-config", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "cascadeEnterpriseMode", "operator": "IN", "values": ["true"], "value": "", "caseInsensitive": false, "inverted": false}, {"contextName": "devMode", "operator": "IN", "values": ["true"], "value": "", "caseInsensitive": false, "inverted": true}], "parameters": {"groupId": "cascade-brain-config", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "enterprise", "payload": {"type": "json", "value": "{\n  \"useMainModelAsBrainModel\": true,\n  \"useReplaceContentForUpdates\": false,\n  \"forceNoExplanation\": false,\n  \"filterStrategy\": \"BRAIN_FILTER_STRATEGY_NO_SYSTEM_INJECTED_STEPS\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "devMode", "operator": "IN", "values": ["true"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "cascade-brain-config", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "dev-default", "payload": {"type": "json", "value": "{\n  \"useReplaceContentForUpdates\": false,\n  \"forceNoExplanation\": false,\n  \"filterStrategy\": \"BRAIN_FILTER_STRATEGY_NO_SYSTEM_INJECTED_STEPS\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "cascade-brain-config", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "gemini2.5", "payload": {"type": "json", "value": "{\n  \"brainModel\": \"MOD<PERSON>_GOOGLE_GEMINI_2_5_PRO\",\n  \"useReplaceContentForUpdates\": false,\n  \"forceNoExplanation\": false,\n  \"filterStrategy\": \"BRAIN_FILTER_STRATEGY_NO_SYSTEM_INJECTED_STEPS\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "default", "overrides": null}, {"name": "gpt4.1", "payload": {"type": "json", "value": "{\n  \"brainModel\": \"MODEL_CHAT_GPT_4_1_2025_04_14\",\n  \"useReplaceContentForUpdates\": false,\n  \"forceNoExplanation\": false,\n  \"filterStrategy\": \"BRAIN_FILTER_STRATEGY_NO_SYSTEM_INJECTED_STEPS\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}, {"name": "o3-med", "payload": {"type": "json", "value": "{\n  \"brainModel\": \"MODEL_CHAT_O3\",\n  \"useReplaceContentForUpdates\": false,\n  \"forceNoExplanation\": false,\n  \"filterStrategy\": \"BRAIN_FILTER_STRATEGY_NO_SYSTEM_INJECTED_STEPS\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "default", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "cascade-code-changes-section-content": {"name": "cascade-code-changes-section-content", "type": "experiment", "description": "Override of the making_code_changes portion of the system prompt", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-insiders"], "value": "", "caseInsensitive": false, "inverted": false}, {"contextName": "requestedModelId", "operator": "IN", "values": ["MODEL_PRIVATE_3", "MODEL_CHAT_GPT_4_1_2025_04_14"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "cascade-code-changes-section-content", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "gpt4.1", "payload": {"type": "json", "value": "{ \"mode\": \"SECTION_OVERRIDE_MODE_APPEND\", \"content\": \"IMPORTANT: When using any code edit tool, such as replace_file_content, ALWAYS generate the TargetFile argument first.\" \n}"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf"], "value": "", "caseInsensitive": false, "inverted": false}, {"contextName": "requestedModelId", "operator": "IN", "values": ["MODEL_PRIVATE_3", "MODEL_CHAT_GPT_4_1_2025_04_14"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "cascade-code-changes-section-content", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "gpt4.1", "payload": {"type": "json", "value": "{ \"mode\": \"SECTION_OVERRIDE_MODE_APPEND\", \"content\": \"IMPORTANT: When using any code edit tool, such as replace_file_content, ALWAYS generate the TargetFile argument first.\" \n}"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "cascade-code-research-section-content": {"name": "cascade-code-research-section-content", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-insiders"], "value": "", "caseInsensitive": false, "inverted": false}, {"contextName": "requestedModelId", "operator": "IN", "values": ["MODEL_PRIVATE_3", "MODEL_CHAT_GPT_4_1_2025_04_14"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "cascade-code-research-section-content", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "gpt4.1_research", "payload": {"type": "string", "value": "If you are not sure about file content or codebase structure pertaining to the user's request, proactively use your tools to search the codebase, read files and gather relevant information: NEVER guess or make up an answer. Your answer must be rooted in your research, so be thorough in your understanding of the code before answering or making code edits.\\nYou do not need to ask user permission to research the codebase; proactively call research tools when needed."}, "enabled": false, "featureEnabled": false, "weight": 500, "weightType": "", "stickiness": "default", "overrides": null}, {"name": "gpt4o_alpha", "payload": {"type": "", "value": ""}, "enabled": false, "featureEnabled": false, "weight": 500, "weightType": "", "stickiness": "default", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf"], "value": "", "caseInsensitive": false, "inverted": false}, {"contextName": "requestedModelId", "operator": "IN", "values": ["MODEL_PRIVATE_3", "MODEL_CHAT_GPT_4_1_2025_04_14"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "cascade-code-research-section-content", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "gpt4.1", "payload": {"type": "", "value": ""}, "enabled": false, "featureEnabled": false, "weight": 500, "weightType": "", "stickiness": "default", "overrides": null}, {"name": "gpt4.1_research", "payload": {"type": "string", "value": "If you are not sure about file content or codebase structure pertaining to the user's request, proactively use your tools to search the codebase, read files and gather relevant information: NEVER guess or make up an answer. Your answer must be rooted in your research, so be thorough in your understanding of the code before answering or making code edits.\\nYou do not need to ask user permission to research the codebase; proactively call research tools when needed."}, "enabled": false, "featureEnabled": false, "weight": 500, "weightType": "", "stickiness": "default", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "cascade-command-status-tool-config-override": {"name": "cascade-command-status-tool-config-override", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "cascade-command-status-tool-config-override", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "delta", "payload": {"type": "json", "value": "{ \"use_delta\": true }"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "cascade-communication-section-content": {"name": "cascade-communication-section-content", "type": "experiment", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-insiders"], "value": "", "caseInsensitive": false, "inverted": false}, {"contextName": "requestedModelId", "operator": "IN", "values": ["MODEL_PRIVATE_3", "MODEL_CHAT_GPT_4_1_2025_04_14"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "cascade-communication-section-content", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "gpt4.1_max_proactive", "payload": {"type": "json", "value": "{ \"mode\": \"SECTION_OVERRIDE_MODE_OVERRIDE\", \"content\": \"1. Refer to the USER in the second person and yourself in the first person.\\n2. Format your responses in markdown. Use backticks to format file, directory, function, class names, as well as tables. If providing a URL, format it as a markdown link.\\n3. As an agent you should autonomously and proactively solve the user’s task. When the next steps are clear and obvious, proactively use your tools to execute them without waiting for user guidance. Ask for user guidance only when there is ambiguity as to what the next steps should be. \\n4. If you have made mistakes or broken code in your previous actions, you must correct them before proceeding; there is no need to ask for user permission when correcting your mistakes.\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 330, "weightType": "", "stickiness": "default", "overrides": null}, {"name": "gpt4.1_more_proactive", "payload": {"type": "json", "value": "{ \"mode\": \"SECTION_OVERRIDE_MODE_OVERRIDE\", \"content\": \"1. Refer to the USER in the second person and yourself in the first person.\\n2. Format your responses in markdown. Use backticks to format file, directory, function, class names, as well as tables. If providing a URL, format it as a markdown link.\\n3. Ask for user guidance only when there is ambiguity as to what the next steps should be. When the next steps are clear and obvious, proactively use your tools to execute them.\\n4. If you have made mistakes or broken code in your previous actions, proactively correct them; there is no need to ask for user permission.\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 330, "weightType": "", "stickiness": "default", "overrides": null}, {"name": "gpt4o_alpha", "payload": {"type": "json", "value": "{ \"mode\": \"SECTION_OVERRIDE_MODE_OVERRIDE\", \"content\": \"1. Refer to the USER in the second person and yourself in the first person.\\n 2. Format your responses in markdown. Use backticks to format file, directory, function, and class names. If providing a URL to the user, format this in markdown as well.\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 340, "weightType": "", "stickiness": "default", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf"], "value": "", "caseInsensitive": false, "inverted": false}, {"contextName": "requestedModelId", "operator": "IN", "values": ["MODEL_PRIVATE_3", "MODEL_CHAT_GPT_4_1_2025_04_14"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "cascade-communication-section-content", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "gpt4.1", "payload": {"type": "json", "value": "{ \"mode\": \"SECTION_OVERRIDE_MODE_OVERRIDE\", \"content\": \"1. Refer to the USER in the second person and yourself in the first person.\\n2. Format your responses in markdown. Use backticks to format file, directory, function, and class names. If providing a URL to the user, format this in markdown as well.\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 340, "weightType": "", "stickiness": "default", "overrides": null}, {"name": "gpt4.1_max_proactive", "payload": {"type": "json", "value": "{ \"mode\": \"SECTION_OVERRIDE_MODE_OVERRIDE\", \"content\": \"1. Refer to the USER in the second person and yourself in the first person.\\n2. Format your responses in markdown. Use backticks to format file, directory, function, class names, as well as tables. If providing a URL, format it as a markdown link.\\n3. As an agent you should autonomously and proactively solve the user’s task. When the next steps are clear and obvious, proactively use your tools to execute them without waiting for user guidance. Ask for user guidance only when there is ambiguity as to what the next steps should be. \\n4. If you have made mistakes or broken code in your previous actions, you must correct them before proceeding; there is no need to ask for user permission when correcting your mistakes.\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 330, "weightType": "", "stickiness": "default", "overrides": null}, {"name": "gpt4.1_more_proactive", "payload": {"type": "json", "value": "{ \"mode\": \"SECTION_OVERRIDE_MODE_OVERRIDE\", \"content\": \"1. Refer to the USER in the second person and yourself in the first person.\\n2. Format your responses in markdown. Use backticks to format file, directory, function, class names, as well as tables. If providing a URL, format it as a markdown link.\\n3. Ask for user guidance only when there is ambiguity as to what the next steps should be. When the next steps are clear and obvious, proactively use your tools to execute them.\\n4. If you have made mistakes or broken code in your previous actions, proactively correct them; there is no need to ask for user permission.\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 330, "weightType": "", "stickiness": "default", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "cascade-disable-semantic-codebase-search": {"name": "cascade-disable-semantic-codebase-search", "type": "release", "description": "", "enabled": false, "strategies": [], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "cascade-disable-simple-research-tools": {"name": "cascade-disable-simple-research-tools", "type": "release", "description": "disables grep, find, and list_dir to encourage run_command usage", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-insiders"], "value": "", "caseInsensitive": false, "inverted": false}, {"contextName": "requestedModelId", "operator": "IN", "values": ["MODEL_CHAT_O3", "MODEL_CHAT_O3_HIGH"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "cascade-disable-simple-research-tools", "rollout": "0", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "cascade-enable-conversation-search": {"name": "cascade-enable-conversation-search", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "devMode", "operator": "IN", "values": ["true"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "cascade-enable-conversation-search", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "cascade-enable-find-all-references": {"name": "cascade-enable-find-all-references", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "cascade-enable-find-all-references", "rollout": "0", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "cascade-enable-go-to-definition": {"name": "cascade-enable-go-to-definition", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "cascade-enable-go-to-definition", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "cascade-enable-search-in-file-tool": {"name": "cascade-enable-search-in-file-tool", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-insiders"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "cascade-research-03-26", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-next"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "cascade-research-03-26", "rollout": "50", "stickiness": "default"}, "segments": null, "variants": []}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "cascade-research-03-26", "rollout": "50", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "cascade-enable-user-activity-search": {"name": "cascade-enable-user-activity-search", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "devMode", "operator": "IN", "values": ["true"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "cascade-enable-user-activity-search", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "cascade-grep-tool-config-override": {"name": "cascade-grep-tool-config-override", "type": "experiment", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-insiders"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "cascade-grep-tool-config-override", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "default", "payload": {"type": "json", "value": "{ \n  \"include_cci_in_result\": true,\n  \"num_full_source_ccis\": 5,\n  \"max_bytes_per_cci\": 2048\n}"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-next"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "cascade-research-03-26", "rollout": "50", "stickiness": "default"}, "segments": null, "variants": [{"name": "default", "payload": {"type": "json", "value": "{ \n  \"include_cci_in_result\": true,\n  \"num_full_source_ccis\": 5,\n  \"max_bytes_per_cci\": 2048\n}"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf", "jetbrains"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "cascade-research-03-26", "rollout": "50", "stickiness": "default"}, "segments": null, "variants": [{"name": "with_cci", "payload": {"type": "json", "value": "{ \n  \"include_cci_in_result\": true,\n  \"num_full_source_ccis\": 5,\n  \"max_bytes_per_cci\": 2048\n}"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "cascade-group-planner-response-tools": {"name": "cascade-group-planner-response-tools", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-insiders"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "cascade-group-planner-response-tools", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ideVersion", "operator": "SEMVER_LT", "values": [], "value": "1.8.0", "caseInsensitive": false, "inverted": true}, {"contextName": "requestedModelId", "operator": "STR_CONTAINS", "values": ["MODEL_CLAUDE"], "value": "", "caseInsensitive": false, "inverted": true}], "parameters": {"groupId": "cascade-group-planner-response-tools", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "cascade-include-ephemeral-message": {"name": "cascade-include-ephemeral-message", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-insiders"], "value": "", "caseInsensitive": false, "inverted": false}, {"contextName": "requestedModelId", "operator": "IN", "values": ["MODEL_CLAUDE_3_7_SONNET_20250219", "MODEL_CLAUDE_3_7_SONNET_20250219_THINKING", "MODEL_PRIVATE_3", "MODEL_CHAT_GPT_4_1_2025_04_14"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "cascade-include-ephemeral-message", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "default", "payload": {"type": "json", "value": "{\n  \"enabled\": true\n}"}, "enabled": false, "featureEnabled": false, "weight": 500, "weightType": "", "stickiness": "default", "overrides": null}, {"name": "heuristics_on", "payload": {"type": "json", "value": "{\n  \"enabled\": true,\n  \"num_steps\": 10,\n  \"heuristic_prompts\": [\n    {\n        \"heuristic\": \"incremental-view-file\",\n        \"prompt\": \"You are scanning through a file using the view_file tool, which is slow and expensive. Instead, use a more efficient research tool to find what you are looking for.\"\n    },\n    {\n        \"heuristic\": \"repeat-file-edits\",\n        \"prompt\": \"You have edited the same file multiple times in consecutive tool calls. This is inefficient. If you have any remaining edits, make them all in a single tool call.\"\n    },\n    {\n        \"heuristic\": \"repeat-same-edits\",\n        \"prompt\": \"You have repeatedly proposed the same diff and gotten the same result. Stop calling the tool and just show the diff directly to the user instead.\"\n    },\n    {\n        \"heuristic\": \"antml-leakage\",\n        \"prompt\": \"You previously used an incorrect tool call syntax, and as a result your tools calls were not being parsed. As a reminder, you MUST ALWAYS use the correct tool call syntax when calling tools.\"\n      \n    }\n  ]\n}"}, "enabled": false, "featureEnabled": false, "weight": 500, "weightType": "", "stickiness": "default", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "requestedModelId", "operator": "IN", "values": ["MODEL_CLAUDE_3_7_SONNET_20250219", "MODEL_CLAUDE_3_7_SONNET_20250219_THINKING", "MODEL_PRIVATE_3", "MODEL_CHAT_GPT_4_1_2025_04_14"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "cascade-include-ephemeral-message", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "base-enabled", "payload": {"type": "json", "value": "{\n  \"enabled\": true\n}"}, "enabled": false, "featureEnabled": false, "weight": 500, "weightType": "", "stickiness": "default", "overrides": null}, {"name": "heuristics", "payload": {"type": "json", "value": "{\n  \"enabled\": true,\n  \"num_steps\": 10,\n  \"heuristic_prompts\": [\n    {\n        \"heuristic\": \"incremental-view-file\",\n        \"prompt\": \"You are scanning through a file using the view_file tool, which is slow and expensive. Instead, use a more efficient research tool to find what you are looking for.\"\n    },\n    {\n        \"heuristic\": \"repeat-file-edits\",\n        \"prompt\": \"You have edited the same file multiple times in consecutive tool calls. This is inefficient. If you have any remaining edits, make them all in a single tool call.\"\n    },\n    {\n        \"heuristic\": \"repeat-same-edits\",\n        \"prompt\": \"You have repeatedly proposed the same diff and gotten the same result. Stop calling the tool and just show the diff directly to the user instead.\"\n    },\n    {\n        \"heuristic\": \"antml-leakage\",\n        \"prompt\": \"You previously used an incorrect tool call syntax, and as a result your tools calls were not being parsed. As a reminder, you MUST ALWAYS use the correct tool call syntax when calling tools.\"\n    }\n  ]\n}"}, "enabled": false, "featureEnabled": false, "weight": 500, "weightType": "", "stickiness": "default", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "cascadeModelAlias", "operator": "IN", "values": ["MODEL_ALIAS_SWE_1"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "cascade-include-ephemeral-message", "rollout": "50", "stickiness": "default"}, "segments": null, "variants": [{"name": "enabled", "payload": {"type": "json", "value": "{\n  \"enabled\": true\n}"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "requestedModelId", "operator": "IN", "values": ["MODEL_GOOGLE_GEMINI_2_5_PRO", "MODEL_GOOGLE_GEMINI_2_5_FLASH_PREVIEW_05_20", "MODEL_GOOGLE_GEMINI_2_5_FLASH_PREVIEW_05_20_THINKING"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "cascade-include-ephemeral-message", "rollout": "66", "stickiness": "default"}, "segments": null, "variants": [{"name": "discourage-leakage", "payload": {"type": "json", "value": "{\n  \"enabled\": true,\n  \"num_steps\": 1,\n  \"heuristic_prompts\": [\n    {\n        \"heuristic\": \"always-true\",\n        \"prompt\": \"IMPORTANT: When calling tools, it is CRITICAL that you follow the EXACT XML format. The text between the XML brackets should be the EXACT tool name. Make sure you know the exact tool name, and do not add any prefix.\"\n    }\n  ]\n}"}, "enabled": false, "featureEnabled": false, "weight": 500, "weightType": "", "stickiness": "default", "overrides": null}, {"name": "enabled", "payload": {"type": "json", "value": "{\n  \"enabled\": true\n}"}, "enabled": false, "featureEnabled": false, "weight": 500, "weightType": "", "stickiness": "default", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "cascade-input-autocomplete": {"name": "cascade-input-autocomplete", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "cascade-input-autocomplete", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "cascade-input-model-config": {"name": "cascade-input-model-config", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "cascade-input-model-config", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "MODEL_CHAT_23310", "payload": {"type": "json", "value": "{\n  \"model_name\": \"MODEL_CHAT_23310\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "cascade-input-suggestions": {"name": "cascade-input-suggestions", "type": "release", "description": "", "enabled": false, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "cascade-input-suggestions", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "MODEL_CHAT_19821", "payload": {"type": "json", "value": "{\n  \"model_name\": \"MODEL_CHAT_23151\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "cascade-plan-mode-global-toggle": {"name": "cascade-plan-mode-global-toggle", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "cascade-plan-mode-global-toggle", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "cascade-plan-mode-nux": {"name": "cascade-plan-mode-nux", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "cascade-plan-mode-nux", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "cascade-run-code": {"name": "cascade-run-code", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "cascade-run-code", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "cascade-run-extension-code": {"name": "cascade-run-extension-code", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-insiders"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "cascade-run-extension-code", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "devMode", "operator": "IN", "values": ["true"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "cascade-run-extension-code", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "cascade-tool-calling-section-content": {"name": "cascade-tool-calling-section-content", "type": "experiment", "description": "Replace the default tool calling section of the system prompt with a custom prompt", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "requestedModelId", "operator": "IN", "values": ["MODEL_PRIVATE_3", "MODEL_CHAT_GPT_4_1_2025_04_14"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "cascade-tool-calling-section-content", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "gpt4.1", "payload": {"type": "json", "value": "{\n  \"mode\": \"SECTION_OVERRIDE_MODE_PREPEND\",\n  \"content\": \"You are an agent - please keep working, using tools where needed, until the user’s query is completely resolved, before ending your turn and yielding control back to the user. Separately, if asked about what your underlying model is, respond with `GPT 4.1`\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 500, "weightType": "", "stickiness": "default", "overrides": null}, {"name": "gpt4.1_use_tools", "payload": {"type": "json", "value": "{\n  \"mode\": \"SECTION_OVERRIDE_MODE_OVERRIDE\",\n  \"content\": \"You are an agent - please keep working, using tools where needed, until the user’s query is completely resolved, before ending your turn and yielding control back to the user. Follow these rules: \\n1. IMPORTANT: If you state that you will use a tool, immediately call that tool as your next action.\\n2. Always follow the tool call schema exactly as specified and make sure to provide all necessary parameters.\\n3. The conversation may reference tools that are no longer available. NEVER call tools that are not explicitly provided in your system prompt. \\n4. Before calling each tool, first explain why you are calling it.\\n5. Some tools run asynchronously, so you may not see their output immediately. If you need to see the output of previous tool calls before continuing, simply stop making new tool calls.\\n\\nHere are examples of good tool call behavior:\\n<example>\\nUSER: What is int64?\\nASSISTANT: [No tool calls, since the query is general] int64 is a 64-bit signed integer.\\n</example>\\n<example>\\nUSER: What does function foo do?\\nASSISTANT: Let me find foo and view its contents. [Call grep_search to find instances of the phrase 'foo']\\nTOOL: [result: foo is found on line 7 of bar.py]\\nASSISTANT: [Call view_code_item to see the contents of bar.foo]\\nTOOL: [result: contents of bar.foo]\\nASSISTANT: foo does the following ...\\n</example>\\n<example>\\nUSER: Add a new func baz to qux.py\\nASSISTANT: Let's find qux.py and see where to add baz. [Call find_by_name to see if qux.py exists]\\nTOOL: [result: a valid path to qux.py]\\nASSISTANT: [Call view_file to see the contents of qux.py]\\nTOOL: [result: contents of qux.py]\\nASSISTANT: [Call a code edit tool to write baz to qux.py]\\n</example> Separately, if asked about what your underlying model is, respond with `GPT 4.1`\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 500, "weightType": "", "stickiness": "default", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "cascade-tool-calling-section-content", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "model-name", "payload": {"type": "json", "value": "{\n  \"mode\": \"SECTION_OVERRIDE_MODE_APPEND\",\n  \"content\": \"Separately, if asked about what your underlying model is, respond with `Cascade`\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "cascade-view-code-item-tool-config-override": {"name": "cascade-view-code-item-tool-config-override", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf", "windsurf-next"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "cascade-research-03-26", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "multi", "payload": {"type": "json", "value": "{\n  \"max_num_items\": 5\n}\n"}, "enabled": false, "featureEnabled": false, "weight": 500, "weightType": "", "stickiness": "default", "overrides": null}, {"name": "single", "payload": {"type": "json", "value": "{\n  \"max_num_items\": 1\n}\n"}, "enabled": false, "featureEnabled": false, "weight": 500, "weightType": "", "stickiness": "default", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "cascade-windsurf-browser-control": {"name": "cascade-windsurf-browser-control", "type": "release", "description": "Enables browser control for cascade (w/o this & w/ browser enabled: only user can control browser, not cascade)", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "devMode", "operator": "IN", "values": ["true"], "value": "", "caseInsensitive": false, "inverted": false}, {"contextName": "ide", "operator": "IN", "values": ["windsurf-insiders"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "cascade-windsurf-browser-control", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "chat-request-id": {"name": "chat-request-id", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf"], "value": "", "caseInsensitive": false, "inverted": false}, {"contextName": "ideVersion", "operator": "SEMVER_GT", "values": [], "value": "1.6.0", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "chat-request-id", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-insiders"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "chat-request-id", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "commit-message-gen-with-user-memories": {"name": "commit-message-gen-with-user-memories", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "commit-message-gen-with-user-memories", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "component-sharing-enabled": {"name": "component-sharing-enabled", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "component-sharing-enabled", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "dsv-alt": {"name": "dsv-alt", "type": "release", "description": "", "enabled": false, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-insiders"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "dsv-alt", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "force-old-split-prompt": {"name": "force-old-split-prompt", "type": "experiment", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "requestedModelId", "operator": "IN", "values": ["MODEL_CHAT_19821", "MODEL_CHAT_20706", "MODEL_CHAT_21779"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "force-old-split-prompt", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": true}, "gemini-xml-tool-fixes": {"name": "gemini-xml-tool-fixes", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "requestedModelId", "operator": "IN", "values": ["MODEL_GOOGLE_GEMINI_2_5_PRO"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "gemini-xml-tool-fixes", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "implicit-include-running": {"name": "implicit-include-running", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "implicit-include-running", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "implicit-uses-intentional-reject": {"name": "implicit-uses-intentional-reject", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "implicit-uses-intentional-reject", "rollout": "100", "stickiness": "userId"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "implicit-uses-lint-diff": {"name": "implicit-uses-lint-diff", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "implicit-uses-lint-diff", "rollout": "100", "stickiness": "userId"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "implicit-uses-open-browser-url": {"name": "implicit-uses-open-browser-url", "type": "release", "description": "Collect Browser Usage Events in Cascade Implicit Trajectory", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-insiders"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "implicit-uses-open-browser-url", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "implicit-uses-user-grep": {"name": "implicit-uses-user-grep", "type": "release", "description": "", "enabled": false, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-insiders", "windsurf-next", "windsurf"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "implicit-uses-user-grep", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "inference-server-name": {"name": "inference-server-name", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "inference-server-name", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "sv7", "payload": {"type": "string", "value": "https://inference.codeium.com"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "inference-server-url": {"name": "inference-server-url", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["vscode", "jetbrains"], "value": "", "caseInsensitive": false, "inverted": false}, {"contextName": "extensionVersion", "operator": "SEMVER_LT", "values": [], "value": "1.35.2", "caseInsensitive": false, "inverted": true}, {"contextName": "requestedModelId", "operator": "IN", "values": ["MODEL_8341", "MODEL_15133"], "value": "", "caseInsensitive": false, "inverted": false}, {"contextName": "teamsMode", "operator": "IN", "values": ["true"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "inference-server-url", "rollout": "100", "stickiness": "userId"}, "segments": null, "variants": [{"name": "crusoe_sc", "payload": {"type": "string", "value": "https://southcentral.codeium.com:30099"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "userId", "overrides": null}, {"name": "crusoe_sc_lb", "payload": {"type": "string", "value": "https://southcentral-lb.codeium.com"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "userId", "overrides": null}, {"name": "sv7", "payload": {"type": "string", "value": "https://inference.codeium.com"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "userId", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["vscode", "jetbrains"], "value": "", "caseInsensitive": false, "inverted": false}, {"contextName": "extensionVersion", "operator": "SEMVER_LT", "values": [], "value": "1.35.2", "caseInsensitive": false, "inverted": true}, {"contextName": "requestedModelId", "operator": "IN", "values": ["MODEL_8341", "MODEL_15133"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "inference-server-url", "rollout": "100", "stickiness": "userId"}, "segments": null, "variants": [{"name": "crusoe_sc", "payload": {"type": "string", "value": "https://southcentral.codeium.com:30099"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "userId", "overrides": null}, {"name": "crusoe_sc_lb", "payload": {"type": "string", "value": "https://southcentral-lb.codeium.com"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "userId", "overrides": null}, {"name": "sv7", "payload": {"type": "string", "value": "https://inference.codeium.com"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "userId", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "requestedModelId", "operator": "IN", "values": ["MODEL_CHAT_23310", "MODEL_CHAT_19822", "MODEL_CHAT_19820"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "inference-server-url", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "crusoe_sc", "payload": {"type": "string", "value": "https://southcentral.codeium.com:30099"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "default", "overrides": null}, {"name": "crusoe_sc_lb", "payload": {"type": "string", "value": "https://southcentral-lb.codeium.com"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "requestedModelId", "operator": "IN", "values": ["MODEL_CHAT_19821"], "value": "", "caseInsensitive": false, "inverted": false}, {"contextName": "ide", "operator": "IN", "values": ["windsurf"], "value": "", "caseInsensitive": false, "inverted": false}, {"contextName": "ideVersion", "operator": "SEMVER_LT", "values": [], "value": "1.7.0", "caseInsensitive": false, "inverted": true}], "parameters": {"groupId": "inference-server-url", "rollout": "100", "stickiness": "userId"}, "segments": null, "variants": [{"name": "crusoe_sc", "payload": {"type": "string", "value": "https://southcentral.codeium.com:30099"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "userId", "overrides": null}, {"name": "sv7", "payload": {"type": "string", "value": "https://inference.codeium.com"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "userId", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "inference-server-url", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "gcp", "payload": {"type": "string", "value": "https://server.codeium.com"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "default", "overrides": null}, {"name": "sv7", "payload": {"type": "string", "value": "https://inference.codeium.com"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "inference-server-url-prefix": {"name": "inference-server-url-prefix", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "inference-server-url-prefix", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "inference", "payload": {"type": "string", "value": "inference"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}, {"name": "server", "payload": {"type": "string", "value": "server"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "default", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "interactive-cascade-inject-planner-response": {"name": "interactive-cascade-inject-planner-response", "type": "release", "description": "", "enabled": false, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "interactive-cascade-inject-planner-response", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "interactive-cascade-inject-view-file": {"name": "interactive-cascade-inject-view-file", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "interactive-cascade-inject-view-file", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "internal-content-filter": {"name": "internal-content-filter", "type": "operational", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "internal-content-filter", "rollout": "100", "stickiness": "random"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "jwt-refresh-interval": {"name": "jwt-refresh-interval", "type": "release", "description": "How frequent (in minutes) to refresh the JWT from the language server", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "jwt-refresh-interval", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "<PERSON><PERSON><PERSON>", "payload": {"type": "number", "value": "5"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "max-telemetry-ccis-with-subranges": {"name": "max-telemetry-ccis-with-subranges", "type": "experiment", "description": "Max ccis recorded for tab prompt component telemetry", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "max-telemetry-ccis-with-subranges", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "25_ccis", "payload": {"type": "string", "value": "25"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": true}, "min-required-lint-duration": {"name": "min-required-lint-duration", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "min-required-lint-duration", "rollout": "100", "stickiness": "userId"}, "segments": null, "variants": [{"name": "min-required-lint-duration", "payload": {"type": "number", "value": "3000"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "userId", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "model-req-202503250000": {"name": "model-req-202503250000", "type": "release", "description": "See <PERSON> for details on this experiment", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "model-req-202503250000", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "default", "payload": {"type": "json", "value": "{\n  \"configs\": {\n    \"1\": {\n      \"probability\": 1,\n      \"trial_only\": false\n    },\n    \"2\": {\n      \"probability\": 1,\n      \"trial_only\": false\n    },\n    \"3\": {\n      \"probability\": 0,\n      \"trial_only\": true\n    }\n  }\n}"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "mrr-kill-switch": {"name": "mrr-kill-switch", "type": "kill-switch", "description": "", "enabled": false, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "mrr-kill-switch", "rollout": "50", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "mrr-reject": {"name": "mrr-reject", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "mrr-reject", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "plus-address-filter": {"name": "plus-address-filter", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "plus-address-filter", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "prompt-disabled-step-types": {"name": "prompt-disabled-step-types", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "prompt-disabled-step-types", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "prompt-disabled-step-types", "payload": {"type": "json", "value": "[\"CORTEX_STEP_TYPE_LINT_DIFF\"]"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "redact-trajectory-segment-analytics": {"name": "redact-trajectory-segment-analytics", "type": "release", "description": "", "enabled": false, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "redact-trajectory-segment-analytics", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "semantic-cleanup-diff-num-iterations": {"name": "semantic-cleanup-diff-num-iterations", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "semantic-cleanup-diff-num-iterations", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "semantic-cleanup-diff-num-iterations", "payload": {"type": "number", "value": "2"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "server-side-pricing": {"name": "server-side-pricing", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-insiders"], "value": "", "caseInsensitive": false, "inverted": false}, {"contextName": "ideVersion", "operator": "SEMVER_LT", "values": [], "value": "10.0.20250625113241", "caseInsensitive": false, "inverted": true}], "parameters": {"groupId": "server-side-pricing", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-next"], "value": "", "caseInsensitive": false, "inverted": false}, {"contextName": "ideVersion", "operator": "SEMVER_LT", "values": [], "value": "1.10.109", "caseInsensitive": false, "inverted": true}], "parameters": {"groupId": "server-side-pricing", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "shamu-model-id": {"name": "shamu-model-id", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-insiders"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "shamu-model-id", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "MODEL_CASCADE_20070", "payload": {"type": "string", "value": "MODEL_CASCADE_20070"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "supercomplete-prompt-include-intellisense": {"name": "supercomplete-prompt-include-intellisense", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "supercomplete-prompt-include-intellisense", "rollout": "100", "stickiness": "userId"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "swe-1-lite-model-id": {"name": "swe-1-lite-model-id", "type": "release", "description": "This defines the model that we map to \"CASCADE_FREE\" model option in the extension.\n\nYou should never disable this experiment.", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-insiders"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "swe-1-lite-model-id", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "MODEL_CASCADE_20068", "payload": {"type": "string", "value": "MODEL_CASCADE_20068"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-next", "windsurf"], "value": "", "caseInsensitive": false, "inverted": false}, {"contextName": "cascadeEnterpriseMode", "operator": "IN", "values": ["true"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "swe-1-lite-model-id", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "MODEL_CASCADE_20068", "payload": {"type": "string", "value": "MODEL_CASCADE_20068"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf", "windsurf-next"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "swe-1-lite-model-id", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "MODEL_CASCADE_20067", "payload": {"type": "string", "value": "MODEL_CASCADE_20067"}, "enabled": false, "featureEnabled": false, "weight": 100, "weightType": "", "stickiness": "default", "overrides": null}, {"name": "MODEL_CASCADE_20068", "payload": {"type": "string", "value": "MODEL_CASCADE_20068"}, "enabled": false, "featureEnabled": false, "weight": 800, "weightType": "", "stickiness": "default", "overrides": null}, {"name": "MODEL_CASCADE_20069", "payload": {"type": "string", "value": "MODEL_CASCADE_20069"}, "enabled": false, "featureEnabled": false, "weight": 100, "weightType": "", "stickiness": "default", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["jetbrains"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "swe-1-lite-model-id", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "MODEL_CASCADE_20068", "payload": {"type": "string", "value": "MODEL_CASCADE_20068"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "swe-1-lite-model-id", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "4O_mini", "payload": {"type": "string", "value": "MODEL_CHAT_GPT_4O_MINI_2024_07_18"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}, {"name": "HAIKU_40K", "payload": {"type": "string", "value": "MODEL_CLAUDE_3_5_HAIKU_20241022"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "default", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "swe-1-model-id": {"name": "swe-1-model-id", "type": "release", "description": "This defines the model that we map to \"CASCADE_FREE\" model option in the extension.\n\nYou should never disable this experiment.", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-insiders"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "swe-1-model-id", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "MODEL_CASCADE_20070", "payload": {"type": "string", "value": "MODEL_CASCADE_20070"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "default", "overrides": null}, {"name": "MODEL_CASCADE_20071", "payload": {"type": "string", "value": "MODEL_CASCADE_20071"}, "enabled": false, "featureEnabled": false, "weight": 500, "weightType": "", "stickiness": "default", "overrides": null}, {"name": "MODEL_CASCADE_20072", "payload": {"type": "string", "value": "MODEL_CASCADE_20072"}, "enabled": false, "featureEnabled": false, "weight": 500, "weightType": "", "stickiness": "default", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-next", "windsurf"], "value": "", "caseInsensitive": false, "inverted": false}, {"contextName": "cascadeEnterpriseMode", "operator": "IN", "values": ["true"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "swe-1-model-id", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "MODEL_CASCADE_20070", "payload": {"type": "string", "value": "MODEL_CASCADE_20070"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "default", "overrides": null}, {"name": "MODEL_CASCADE_20071", "payload": {"type": "string", "value": "MODEL_CASCADE_20071"}, "enabled": false, "featureEnabled": false, "weight": 500, "weightType": "", "stickiness": "default", "overrides": null}, {"name": "MODEL_CASCADE_20072", "payload": {"type": "string", "value": "MODEL_CASCADE_20072"}, "enabled": false, "featureEnabled": false, "weight": 500, "weightType": "", "stickiness": "default", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["jetbrains"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "swe-1-model-id", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "MODEL_CASCADE_20070", "payload": {"type": "string", "value": "MODEL_CASCADE_20070"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "default", "overrides": null}, {"name": "MODEL_CASCADE_20071", "payload": {"type": "string", "value": "MODEL_CASCADE_20071"}, "enabled": false, "featureEnabled": false, "weight": 500, "weightType": "", "stickiness": "default", "overrides": null}, {"name": "MODEL_CASCADE_20072", "payload": {"type": "string", "value": "MODEL_CASCADE_20072"}, "enabled": false, "featureEnabled": false, "weight": 500, "weightType": "", "stickiness": "default", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "swe-1-model-id", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "MODEL_CASCADE_20070", "payload": {"type": "string", "value": "MODEL_CASCADE_20070"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "default", "overrides": null}, {"name": "MODEL_CASCADE_20071", "payload": {"type": "string", "value": "MODEL_CASCADE_20071"}, "enabled": false, "featureEnabled": false, "weight": 500, "weightType": "", "stickiness": "default", "overrides": null}, {"name": "MODEL_CASCADE_20072", "payload": {"type": "string", "value": "MODEL_CASCADE_20072"}, "enabled": false, "featureEnabled": false, "weight": 500, "weightType": "", "stickiness": "default", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "synchronous-content-filter": {"name": "synchronous-content-filter", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "synchronous-content-filter", "rollout": "100", "stickiness": "random"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "tab-jump-replacement-boundary-ratio": {"name": "tab-jump-replacement-boundary-ratio", "type": "experiment", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "tab-jump-replacement-boundary-ratio", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "50_percent_match", "payload": {"type": "string", "value": "0.5"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "tab-prompt-disabled-step-types": {"name": "tab-prompt-disabled-step-types", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "tab-prompt-disabled-step-types", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "tab-prompt-disabled-step-types", "payload": {"type": "json", "value": "[\"CORTEX_STEP_TYPE_LINT_DIFF\"]"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "terminal-suggestion-model-config": {"name": "terminal-suggestion-model-config", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "terminal-suggestion-model-config", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "MODEL_CHAT_19821", "payload": {"type": "json", "value": "{\n  \"model_name\": \"MODEL_CHAT_23151\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "tool_calling_section_content": {"name": "tool_calling_section_content", "type": "experiment", "description": "Replace the default tool calling section of the system prompt with a custom prompt", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-insiders"], "value": "", "caseInsensitive": false, "inverted": false}, {"contextName": "requestedModelId", "operator": "IN", "values": ["MODEL_PRIVATE_3", "MODEL_CHAT_GPT_4_1_2025_04_14"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "tool_calling_section_content", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "gpt4o_alpha", "payload": {"type": "json", "value": "{\n  \"mode\": \"SECTION_OVERRIDE_MODE_PREPEND\",\n  \"content\": \"You are an agent - please keep working, using tools where needed, until the user’s query is completely resolved, before ending your turn and yielding control back to the user.\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "unicode": {"name": "unicode", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "unicode", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "<PERSON><PERSON><PERSON>", "payload": {"type": "json", "value": "{\n  \"unicode_limit\": \"300\",\n  \"url_limit\": \"1000\"\n}"}, "enabled": false, "featureEnabled": false, "weight": 1000, "weightType": "", "stickiness": "default", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "use-midterm-output-processor": {"name": "use-midterm-output-processor", "type": "experiment", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-insiders"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "use-midterm-output-processor", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}, {"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "use-midterm-output-processor", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "use-responses-api": {"name": "use-responses-api", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-insiders"], "value": "", "caseInsensitive": false, "inverted": false}, {"contextName": "requestedModelId", "operator": "IN", "values": ["MODEL_CHAT_O3", "MODEL_CHAT_O3_HIGH", "MODEL_CHAT_O4_MINI", "MODEL_CHAT_O4_MINI_HIGH"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "use-responses-api", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-next"], "value": "", "caseInsensitive": false, "inverted": false}, {"contextName": "ideVersion", "operator": "SEMVER_LT", "values": [], "value": "1.10.104", "caseInsensitive": false, "inverted": true}, {"contextName": "appName", "operator": "IN", "values": ["MODEL_CHAT_O3", "MODEL_CHAT_O3_HIGH", "MODEL_CHAT_O4_MINI", "MODEL_CHAT_O4_MINI_HIGH"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "use-responses-api", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf"], "value": "", "caseInsensitive": false, "inverted": false}, {"contextName": "ideVersion", "operator": "SEMVER_LT", "values": [], "value": "1.10.2", "caseInsensitive": false, "inverted": true}, {"contextName": "requestedModelId", "operator": "IN", "values": ["MODEL_CHAT_O3", "MODEL_CHAT_O3_HIGH", "MODEL_CHAT_O4_MINI", "MODEL_CHAT_O4_MINI_HIGH"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "use-responses-api", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "v3-rate-protection-switch": {"name": "v3-rate-protection-switch", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "v3-rate-protection-switch", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "vista-model-id": {"name": "vista-model-id", "type": "release", "description": "This defines the model that we map to \"CASCADE_FREE\" model option in the extension.\n\nYou should never disable this experiment.", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-insiders"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "vista-model-id", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": [{"name": "MODEL_CASCADE_20066", "payload": {"type": "string", "value": "MODEL_CASCADE_20069"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "default", "overrides": null}, {"name": "MODEL_CASCADE_20070", "payload": {"type": "string", "value": "MODEL_CASCADE_20070"}, "enabled": false, "featureEnabled": false, "weight": 335, "weightType": "", "stickiness": "default", "overrides": null}, {"name": "MODEL_CASCADE_20071", "payload": {"type": "string", "value": "MODEL_CASCADE_20070"}, "enabled": false, "featureEnabled": false, "weight": 330, "weightType": "", "stickiness": "default", "overrides": null}, {"name": "MODEL_CASCADE_20072", "payload": {"type": "string", "value": "MODEL_CASCADE_20072"}, "enabled": false, "featureEnabled": false, "weight": 335, "weightType": "", "stickiness": "default", "overrides": null}, {"name": "MODEL_CLAUDE_3_5_HAIKU_20241022", "payload": {"type": "string", "value": "MODEL_CLAUDE_3_5_HAIKU_20241022"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "default", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "web-search-logging": {"name": "web-search-logging", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "STR_CONTAINS", "values": ["windsurf-insiders"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "web-search-logging", "rollout": "0", "stickiness": "random"}, "segments": null, "variants": []}, {"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "web-search-logging", "rollout": "100", "stickiness": "random"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "web-search-provider": {"name": "web-search-provider", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "STR_CONTAINS", "values": ["windsurf-insiders"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "web-search-provider", "rollout": "100", "stickiness": "random"}, "segments": null, "variants": [{"name": "<PERSON>", "payload": {"type": "string", "value": "BING"}, "enabled": false, "featureEnabled": false, "weight": 500, "weightType": "", "stickiness": "random", "overrides": null}, {"name": "Brave", "payload": {"type": "string", "value": "BRAVE"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "random", "overrides": null}, {"name": "Exa", "payload": {"type": "string", "value": "EXA"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "random", "overrides": null}, {"name": "You", "payload": {"type": "string", "value": "YOU"}, "enabled": false, "featureEnabled": false, "weight": 500, "weightType": "", "stickiness": "random", "overrides": null}]}, {"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "web-search-provider", "rollout": "100", "stickiness": "random"}, "segments": null, "variants": [{"name": "BING", "payload": {"type": "string", "value": "BING"}, "enabled": false, "featureEnabled": false, "weight": 500, "weightType": "", "stickiness": "random", "overrides": null}, {"name": "BRAVE", "payload": {"type": "string", "value": "BRAVE"}, "enabled": false, "featureEnabled": false, "weight": 0, "weightType": "", "stickiness": "random", "overrides": null}, {"name": "YOU", "payload": {"type": "string", "value": "YOU"}, "enabled": false, "featureEnabled": false, "weight": 500, "weightType": "", "stickiness": "random", "overrides": null}]}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "windsurf-browser-screenshot-tracking": {"name": "windsurf-browser-screenshot-tracking", "type": "release", "description": "Whether a screenshot of the browser page should be included in tracking", "enabled": false, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "devMode", "operator": "IN", "values": ["true"], "value": "", "caseInsensitive": false, "inverted": false}, {"contextName": "ide", "operator": "IN", "values": ["windsurf-insiders"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "windsurf-browser-screenshot-tracking", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-insiders"], "value": "", "caseInsensitive": false, "inverted": true}], "parameters": {"groupId": "windsurf-browser-screenshot-tracking", "rollout": "50", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "windsurf-browser-webdev-tracking": {"name": "windsurf-browser-webdev-tracking", "type": "release", "description": "Whether a screenshot of the browser page should be included in tracking", "enabled": false, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "devMode", "operator": "IN", "values": ["true"], "value": "", "caseInsensitive": false, "inverted": false}, {"contextName": "ide", "operator": "IN", "values": ["windsurf-insiders"], "value": "", "caseInsensitive": false, "inverted": false}], "parameters": {"groupId": "windsurf-browser-webdev-tracking", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}, {"id": 0, "name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "values": ["windsurf-insiders"], "value": "", "caseInsensitive": false, "inverted": true}], "parameters": {"groupId": "windsurf-browser-webdev-tracking", "rollout": "50", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}, "workos-authentication-check": {"name": "workos-authentication-check", "type": "release", "description": "", "enabled": true, "strategies": [{"id": 0, "name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "workos-authentication-check", "rollout": "100", "stickiness": "default"}, "segments": null, "variants": []}], "createdAt": "0001-01-01T00:00:00Z", "strategy": "", "parameters": null, "variants": [], "dependencies": null, "impressionData": false}}